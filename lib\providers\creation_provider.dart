import 'package:flutter/material.dart';
import 'package:nsl/screens/web/static_flow/get_all_my_library_model.dart';

class CreationProvider extends ChangeNotifier {
  int _currentMiddleScreen = 2;
  // 0 - roles 1- objects 2-GO
  bool _isGoMyLibraryClicked = false;
  bool _isLeftPanelVisible = true; // Control left panel visibility

  // Role auto-population data
  RolesPostgre? _prePopulatedRole;
  List<RolesPostgre> _allRoles = [];

  // Trigger for adding new role rows
  bool _shouldAddNewRoleRow = false;


  int get currentMiddleScreen => _currentMiddleScreen;
  bool get isGoMyLibraryClicked => _isGoMyLibraryClicked;
  bool get isLeftPanelVisible => _isLeftPanelVisible;
  RolesPostgre? get prePopulatedRole => _prePopulatedRole;
  List<RolesPostgre> get allRoles => _allRoles;
  bool get shouldAddNewRoleRow => _shouldAddNewRoleRow;

  set currentMiddleScreen(value) {
    _currentMiddleScreen = value;
    notifyListeners();
  }

  set isGoMyLibraryClicked(value) {
    _isGoMyLibraryClicked = value;
    notifyListeners();
  }

  set isLeftPanelVisible(value) {
    _isLeftPanelVisible = value;
    notifyListeners();
  }

  /// Show the left panel and switch to the specified tab
  void showLeftPanelWithTab(int tabIndex) {
    _isLeftPanelVisible = true;
    _currentMiddleScreen = tabIndex;
    notifyListeners();
  }

  /// Toggle the left panel visibility
  void toggleLeftPanel() {
    _isLeftPanelVisible = !_isLeftPanelVisible;
    notifyListeners();
  }

  /// Set role data for auto-population in role creation screen
  void setRoleForCreation(RolesPostgre? role, List<RolesPostgre> allRoles) {
    print('=== PROVIDER: setRoleForCreation called ===');
    print('=== PROVIDER: Setting role: ${role?.name}, ID: ${role?.roleId} ===');
    print('=== PROVIDER: Setting ${allRoles.length} roles ===');
    _prePopulatedRole = role;
    _allRoles = allRoles;
    print('=== PROVIDER: Role set. Current role: ${_prePopulatedRole?.name} ===');
    notifyListeners();
    print('=== PROVIDER: notifyListeners() called ===');
  }

  /// Clear role data
  void clearRoleData() {
    print('=== PROVIDER: clearRoleData() called - CLEARING ROLE DATA ===');
    print('=== PROVIDER: Previous role was: ${_prePopulatedRole?.name} ===');
    _prePopulatedRole = null;
    _allRoles = [];
    print('=== PROVIDER: Role data cleared ===');
    notifyListeners();
  }

  /// Trigger adding a new role row
  void triggerAddNewRoleRow() {
    print('=== PROVIDER: triggerAddNewRoleRow() called ===');
    print('=== PROVIDER: Setting _shouldAddNewRoleRow = true ===');
    _shouldAddNewRoleRow = true;
    print('=== PROVIDER: Calling notifyListeners() ===');
    notifyListeners();
    print('=== PROVIDER: notifyListeners() completed ===');
  }

  /// Reset the new role row trigger (called after the row is added)
  void resetAddNewRoleRowTrigger() {
    _shouldAddNewRoleRow = false;
    notifyListeners();
  }
}
