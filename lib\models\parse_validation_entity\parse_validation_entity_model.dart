import 'dart:convert';

/// UNIVERSAL ENTRY POINTS

ParseValidationEntityModel parseValidationEntityModelFromJson(String str) =>
    ParseValidationEntityModel.fromJson(json.decode(str));

String parseValidationEntityModelToJson(ParseValidationEntityModel data) =>
    json.encode(data.toJson());

/// MAIN MODEL
class ParseValidationEntityModel {
  // Universal fields
  final bool? success;
  final String? error;
  final String? operation;
  final bool? isValid;
  final Issues? issues;
  final bool? hasErrors;
  final bool? hasWarnings;
  final IssueCounts? issueCounts;

  // Uniqueness & Validation
  final ValidationResult? validationResult;
  final List<dynamic>? dependencyErrors;
  final UniquenessResult? uniquenessResult;

  // Data (may appear in different keys)
  final ParsedData? parsedData;
  final ParsedData? savedData;
  final ParsedData? existingDocument;

  // Wrapped fields (error style response)
  final String? existingIn;
  final DetailWrapper? detail;

  ParseValidationEntityModel({
    this.success,
    this.error,
    this.operation,
    this.isValid,
    this.issues,
    this.hasErrors,
    this.hasWarnings,
    this.issueCounts,
    this.validationResult,
    this.dependencyErrors,
    this.uniquenessResult,
    this.parsedData,
    this.savedData,
    this.existingDocument,
    this.existingIn,
    this.detail,
  });

  // Preferred: Access main data regardless of response location
  ParsedData? get mainData => existingDocument ?? savedData ?? parsedData;

  // Unified error message (works for detail and base-level)
  String? get errorMessage => error ?? detail?.error;

  // Is operation successful? Most APIs use 'success', but 'detail.success' for error wrapper
  bool get isSuccessful => success == true && (detail?.success != false);

  // Auto-parsing: supports direct, saved, or wrapped-responses
  factory ParseValidationEntityModel.fromJson(Map<String, dynamic> json) {
    // Wrapped error/detail response?
    if (json.containsKey("detail")) {
      final detail =
          DetailWrapper.fromJson(json["detail"] as Map<String, dynamic>);
      return ParseValidationEntityModel(
        detail: detail,
        success: detail.success,
        error: detail.error,
        existingIn: detail.existingIn,
        existingDocument: detail.existingDocument,
        operation: detail.operation,
      );
    }

    // Standard save or parse_validate response
    return ParseValidationEntityModel(
      success: json["success"],
      error: json["error"], // Some APIs set error here
      operation: json["operation"],
      isValid: json["is_valid"],
      hasErrors: json["has_errors"],
      hasWarnings: json["has_warnings"],
      issueCounts: json["issue_counts"] != null
          ? IssueCounts.fromJson(json["issue_counts"])
          : null,
      issues: json["issues"] != null ? Issues.fromJson(json["issues"]) : null,
      validationResult: json["validation_result"] != null
          ? ValidationResult.fromJson(json["validation_result"])
          : null,
      dependencyErrors: json["dependency_errors"] ?? [],
      uniquenessResult: json["uniqueness_result"] != null
          ? UniquenessResult.fromJson(json["uniqueness_result"])
          : null,
      savedData: json["saved_data"] != null
          ? ParsedData.fromJson(json["saved_data"])
          : null,
      parsedData: json["parsed_data"] != null
          ? ParsedData.fromJson(json["parsed_data"])
          : null,
      // Error responses may include existing_document at top-level
      existingDocument: json["existing_document"] != null
          ? ParsedData.fromJson(json["existing_document"])
          : null,
      existingIn: json["existing_in"],
    );
  }

  Map<String, dynamic> toJson() => {
        "success": success,
        "error": error,
        "operation": operation,
        "is_valid": isValid,
        "has_errors": hasErrors,
        "has_warnings": hasWarnings,
        "issue_counts": issueCounts?.toJson(),
        "issues": issues?.toJson(),
        "validation_result": validationResult?.toJson(),
        "dependency_errors": dependencyErrors ?? [],
        "uniqueness_result": uniquenessResult?.toJson(),
        "saved_data": savedData?.toJson(),
        "parsed_data": parsedData?.toJson(),
        "existing_document": existingDocument?.toJson(),
        "existing_in": existingIn,
        "detail": detail?.toJson(),
      };
}

/// Error wrapper for detail-style responses (first JSON example)
class DetailWrapper {
  final bool? success;
  final String? error;
  final String? existingIn;
  final ParsedData? existingDocument;
  final String? operation;

  DetailWrapper({
    this.success,
    this.error,
    this.existingIn,
    this.existingDocument,
    this.operation,
  });

  factory DetailWrapper.fromJson(Map<String, dynamic> json) => DetailWrapper(
        success: json["success"],
        error: json["error"],
        existingIn: json["existing_in"],
        existingDocument: json["existing_document"] != null
            ? ParsedData.fromJson(json["existing_document"])
            : null,
        operation: json["operation"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "error": error,
        "existing_in": existingIn,
        "existing_document": existingDocument?.toJson(),
        "operation": operation,
      };
}

/// ====== Rest of the models (ParsedData, etc.) ======

class ParsedData {
  final String? id;
  final String? entityId;
  final String? name;
  final String? displayName;
  final String? tenantId;
  final String? tenantName;
  final String? businessDomain;
  final String? category;
  final List<String>? tags;
  final String? archivalStrategy;
  final String? icon;
  final String? colourTheme;
  final int? version;
  final String? status;
  final String? type;
  final String? description;
  final String? tableName;
  final String? naturalLanguage;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? createdBy;
  final String? updatedBy;
  final String? entityStatus;
  final List<dynamic>? changesDetected;
  final String? iconType;
  final String? iconContent;

  ParsedData({
    this.id,
    this.entityId,
    this.name,
    this.displayName,
    this.tenantId,
    this.tenantName,
    this.businessDomain,
    this.category,
    this.tags,
    this.archivalStrategy,
    this.icon,
    this.colourTheme,
    this.version,
    this.status,
    this.type,
    this.description,
    this.tableName,
    this.naturalLanguage,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.entityStatus,
    this.changesDetected,
    this.iconType,
    this.iconContent,
  });

  factory ParsedData.fromJson(Map<String, dynamic> json) => ParsedData(
        id: json["_id"],
        entityId: json["entity_id"],
        name: json["name"],
        displayName: json["display_name"],
        tenantId: json["tenant_id"],
        tenantName: json["tenant_name"],
        businessDomain: json["business_domain"],
        category: json["category"],
        tags: json["tags"] == null
            ? []
            : List<String>.from(json["tags"].map((x) => x)),
        archivalStrategy: json["archival_strategy"],
        icon: json["icon"],
        colourTheme: json["colour_theme"],
        version: json["version"],
        status: json["status"],
        type: json["type"],
        description: json["description"],
        tableName: json["table_name"],
        naturalLanguage: json["natural_language"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        entityStatus: json["entity_status"],
        changesDetected: json["changes_detected"] == null
            ? []
            : List<dynamic>.from(json["changes_detected"].map((x) => x)),
        iconType: json["icon_type"],
        iconContent: json["icon_content"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "entity_id": entityId,
        "name": name,
        "display_name": displayName,
        "tenant_id": tenantId,
        "tenant_name": tenantName,
        "business_domain": businessDomain,
        "category": category,
        "tags": tags ?? [],
        "archival_strategy": archivalStrategy,
        "icon": icon,
        "colour_theme": colourTheme,
        "version": version,
        "status": status,
        "type": type,
        "description": description,
        "table_name": tableName,
        "natural_language": naturalLanguage,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_by": updatedBy,
        "entity_status": entityStatus,
        "changes_detected": changesDetected ?? [],
        "icon_type": iconType,
        "icon_content": iconContent,
      };
}

/// ========== Rest of supporting classes unchanged ==========

class ValidationResult {
  final List<dynamic>? structureErrors;
  final List<dynamic>? requiredFieldErrors;
  final List<dynamic>? dataTypeErrors;
  final List<dynamic>? customErrors;

  ValidationResult({
    this.structureErrors,
    this.requiredFieldErrors,
    this.dataTypeErrors,
    this.customErrors,
  });

  factory ValidationResult.fromJson(Map<String, dynamic> json) =>
      ValidationResult(
        structureErrors: json["structure_errors"] ?? [],
        requiredFieldErrors: json["required_field_errors"] ?? [],
        dataTypeErrors: json["data_type_errors"] ?? [],
        customErrors: json["custom_errors"] ?? [],
      );

  Map<String, dynamic> toJson() => {
        "structure_errors": structureErrors ?? [],
        "required_field_errors": requiredFieldErrors ?? [],
        "data_type_errors": dataTypeErrors ?? [],
        "custom_errors": customErrors ?? [],
      };
}

class IssueCounts {
  final int? totalErrors;
  final int? totalWarnings;
  final int? totalExceptions;
  final int? validationErrors;
  final int? dependencyErrors;
  final int? uniquenessIssues;
  final int? parsingIssues;
  final int? mongoErrors;
  final int? postgresErrors;
  final bool? hasCriticalErrors;
  final bool? hasWarnings;

  IssueCounts({
    this.totalErrors,
    this.totalWarnings,
    this.totalExceptions,
    this.validationErrors,
    this.dependencyErrors,
    this.uniquenessIssues,
    this.parsingIssues,
    this.mongoErrors,
    this.postgresErrors,
    this.hasCriticalErrors,
    this.hasWarnings,
  });

  factory IssueCounts.fromJson(Map<String, dynamic> json) => IssueCounts(
        totalErrors: json["total_errors"],
        totalWarnings: json["total_warnings"],
        totalExceptions: json["total_exceptions"],
        validationErrors: json["validation_errors"],
        dependencyErrors: json["dependency_errors"],
        uniquenessIssues: json["uniqueness_issues"],
        parsingIssues: json["parsing_issues"],
        mongoErrors: json["mongo_errors"],
        postgresErrors: json["postgres_errors"],
        hasCriticalErrors: json["has_critical_errors"],
        hasWarnings: json["has_warnings"],
      );

  Map<String, dynamic> toJson() => {
        "total_errors": totalErrors,
        "total_warnings": totalWarnings,
        "total_exceptions": totalExceptions,
        "validation_errors": validationErrors,
        "dependency_errors": dependencyErrors,
        "uniqueness_issues": uniquenessIssues,
        "parsing_issues": parsingIssues,
        "mongo_errors": mongoErrors,
        "postgres_errors": postgresErrors,
        "has_critical_errors": hasCriticalErrors,
        "has_warnings": hasWarnings,
      };
}

class Issues {
  final IssueCounts? summary;
  final List<dynamic>? errors;
  final List<dynamic>? warnings;
  final List<dynamic>? exceptions;
  final List<dynamic>? validationErrors;
  final List<dynamic>? dependencyErrors;
  final List<UniquenessIssue>? uniquenessIssues;
  final List<dynamic>? parsingIssues;
  final List<dynamic>? mongoErrors;
  final List<dynamic>? postgresErrors;

  Issues({
    this.summary,
    this.errors,
    this.warnings,
    this.exceptions,
    this.validationErrors,
    this.dependencyErrors,
    this.uniquenessIssues,
    this.parsingIssues,
    this.mongoErrors,
    this.postgresErrors,
  });

  factory Issues.fromJson(Map<String, dynamic> json) => Issues(
        summary: json["summary"] != null
            ? IssueCounts.fromJson(json["summary"])
            : null,
        errors: json["errors"] ?? [],
        warnings: json["warnings"] ?? [],
        exceptions: json["exceptions"] ?? [],
        validationErrors: json["validation_errors"] ?? [],
        dependencyErrors: json["dependency_errors"] ?? [],
        uniquenessIssues: json["uniqueness_issues"] == null
            ? []
            : List<UniquenessIssue>.from(json["uniqueness_issues"]
                .map((x) => UniquenessIssue.fromJson(x))),
        parsingIssues: json["parsing_issues"] ?? [],
        mongoErrors: json["mongo_errors"] ?? [],
        postgresErrors: json["postgres_errors"] ?? [],
      );

  Map<String, dynamic> toJson() => {
        "summary": summary?.toJson(),
        "errors": errors ?? [],
        "warnings": warnings ?? [],
        "exceptions": exceptions ?? [],
        "validation_errors": validationErrors ?? [],
        "dependency_errors": dependencyErrors ?? [],
        "uniqueness_issues": uniquenessIssues == null
            ? []
            : List<dynamic>.from(uniquenessIssues!.map((x) => x.toJson())),
        "parsing_issues": parsingIssues ?? [],
        "mongo_errors": mongoErrors ?? [],
        "postgres_errors": postgresErrors ?? [],
      };
}

class UniquenessResult {
  final bool? isUnique;
  final String? status;
  final String? message;
  final ParsedData? existingDocument; // NB: "existing_document"

  UniquenessResult({
    this.isUnique,
    this.status,
    this.message,
    this.existingDocument,
  });

  factory UniquenessResult.fromJson(Map<String, dynamic> json) =>
      UniquenessResult(
        isUnique: json["is_unique"],
        status: json["status"],
        message: json["message"],
        existingDocument: json["existing_document"] == null
            ? null
            : ParsedData.fromJson(json["existing_document"]),
      );

  Map<String, dynamic> toJson() => {
        "is_unique": isUnique,
        "status": status,
        "message": message,
        "existing_document": existingDocument?.toJson(),
      };
}

class UniquenessIssue {
  final String? status;
  final String? message;
  final String? source;
  final DateTime? timestamp;
  final UniquenessResult? details;
  final String? existingDocumentId;

  UniquenessIssue({
    this.status,
    this.message,
    this.source,
    this.timestamp,
    this.details,
    this.existingDocumentId,
  });

  factory UniquenessIssue.fromJson(Map<String, dynamic> json) =>
      UniquenessIssue(
        status: json["status"],
        message: json["message"],
        source: json["source"],
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.tryParse(json["timestamp"]),
        details: json["details"] == null
            ? null
            : UniquenessResult.fromJson(json["details"]),
        existingDocumentId: json["existing_document_id"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "source": source,
        "timestamp": timestamp?.toIso8601String(),
        "details": details?.toJson(),
        "existing_document_id": existingDocumentId,
      };
}
