import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/providers/roles_provider.dart';
import 'package:nsl/providers/go_details_provider.dart';
import 'package:nsl/providers/my_library_provider.dart';
import 'package:nsl/models/role_model.dart';
import 'package:nsl/screens/web/static_flow/custom_tooltip.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/static_flow/extract_lo_details.dart';
import 'package:nsl/screens/web/static_flow/customer_onboarding/widgets/custom_dropdown_widget.dart';
import 'package:nsl/utils/logger.dart';
import 'package:provider/provider.dart';

import '../../../models/solution/entity_attribut_details.dart';

/// Helper class for column width constraints
class ColumnConstraints {
  final double minWidth;
  final double maxWidth;

  const ColumnConstraints({
    required this.minWidth,
    required this.maxWidth,
  });
}

class ExtractGoDetailsMiddleStatic extends StatefulWidget {
  final String? sessionId; // New session-based API support
  final String? userIntent;

  const ExtractGoDetailsMiddleStatic({
    super.key,
    this.sessionId,
    this.userIntent,
  });

  @override
  State<ExtractGoDetailsMiddleStatic> createState() =>
      _ExtractGoDetailsMiddleStaticState();
}

class _ExtractGoDetailsMiddleStaticState
    extends State<ExtractGoDetailsMiddleStatic> {
  // Map to store unique keys for each LO item
  final Map<int, GlobalKey> _loTextKeys = {};

  // Map to store unique keys for each Create pathway button
  final Map<int, GlobalKey> _pathwayButtonKeys = {};

  // Hover state for Create pathway button - individual state per LO index
  final Map<int, bool> _isHoveringCreatePathway = {};

  // Overlay entry for hover image
  OverlayEntry? _hoverOverlayEntry;

  get rowIndex => null;

  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startBackgroundSaveFor5mins();
    // Initialize library provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final libraryProvider =
          Provider.of<MyLibraryProvider>(context, listen: false);
      libraryProvider.fetchLibraryData();
    });
  }

  _startBackgroundSaveFor5mins() async {
    final goDetailsProvider =
        Provider.of<GoDetailsProvider>(context, listen: false);
    _timer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      await goDetailsProvider.saveSolutionArtifact();
    });
  }

  /// Gets or creates a unique key for each LO item
  GlobalKey _getLoTextKey(int index) {
    if (!_loTextKeys.containsKey(index)) {
      _loTextKeys[index] = GlobalKey();
    }
    return _loTextKeys[index]!;
  }

  /// Gets or creates a unique key for each pathway button
  GlobalKey _getPathwayButtonKey(int index) {
    if (!_pathwayButtonKeys.containsKey(index)) {
      _pathwayButtonKeys[index] = GlobalKey();
    }
    return _pathwayButtonKeys[index]!;
  }

  /// Sets the hover state for a specific LO index
  void _setPathwayHoverState(int index, bool isHovering) {
    setState(() {
      _isHoveringCreatePathway[index] = isHovering;
    });

    if (isHovering) {
      _showHoverOverlay(index);
    } else {
      _hideHoverOverlay();
    }
  }

  /// Show hover overlay with auto-positioning
  void _showHoverOverlay(int index) {
    _hideHoverOverlay(); // Remove any existing overlay

    final pathwayButtonKey = _getPathwayButtonKey(index);
    final renderBox =
        pathwayButtonKey.currentContext?.findRenderObject() as RenderBox?;

    if (renderBox == null) return;

    final buttonPosition = renderBox.localToGlobal(Offset.zero);
    final buttonSize = renderBox.size;
    final screenSize = MediaQuery.of(context).size;

    const double imageHeight = 180.0;
    const double imageWidth = 600.0;
    const double spacing = 16.0;

    // Calculate optimal position
    double top, left;

    // Determine vertical position
    final spaceAbove = buttonPosition.dy;
    final spaceBelow =
        screenSize.height - (buttonPosition.dy + buttonSize.height);

    if (spaceAbove >= imageHeight + spacing) {
      // Position above
      top = buttonPosition.dy - imageHeight - spacing;
    } else if (spaceBelow >= imageHeight + spacing) {
      // Position below
      top = buttonPosition.dy + buttonSize.height + spacing;
    } else {
      // Choose side with more space
      if (spaceAbove > spaceBelow) {
        top = spacing; // Position at top of screen
      } else {
        top = buttonPosition.dy + buttonSize.height + spacing;
      }
    }

    // Determine horizontal position
    final spaceLeft = buttonPosition.dx;
    final spaceRight =
        screenSize.width - (buttonPosition.dx + buttonSize.width);

    if (spaceLeft >= 50 && spaceRight >= imageWidth - 50) {
      // Preferred position - slightly to the left
      left = buttonPosition.dx - 50;
    } else if (spaceRight >= imageWidth + spacing) {
      // Position to the right
      left = buttonPosition.dx + buttonSize.width + spacing;
    } else if (spaceLeft >= imageWidth + spacing) {
      // Position to the left
      left = buttonPosition.dx - imageWidth - spacing;
    } else {
      // Center in screen
      left = (screenSize.width - imageWidth) / 2;
    }

    // Ensure image stays within screen bounds
    left = left.clamp(spacing, screenSize.width - imageWidth - spacing);
    top = top.clamp(spacing, screenSize.height - imageHeight - spacing);

    _hoverOverlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: top,
        left: left,
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: imageWidth,
            height: imageHeight,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.15),
                  offset: const Offset(0, 4),
                  blurRadius: 12,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.asset(
                'assets/images/pathway_hint.gif',
                width: imageWidth,
                height: imageHeight,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: imageWidth,
                    height: imageHeight,
                    color: Colors.grey[100],
                    child: const Center(
                      child: Text(
                        'Pathway Hint Image',
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_hoverOverlayEntry!);
  }

  /// Hide hover overlay
  void _hideHoverOverlay() {
    _hoverOverlayEntry?.remove();
    _hoverOverlayEntry = null;
  }

  @override
  void dispose() {
    // Clear hover states to prevent memory leaks
    _hideHoverOverlay();
    _isHoveringCreatePathway.clear();
    _pathwayButtonKeys.clear();
    _timer?.cancel();
    super.dispose();
  }

  /// Helper method to build action buttons (check and cross) according to design
  Widget _buildActionButton({
    required String text,
    required VoidCallback onTap,
    required Color backgroundColor,
    required Color textColor,
    Color? borderColor,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        height: 24,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: backgroundColor,
          border: borderColor != null
              ? Border.all(color: borderColor, width: 1)
              : null,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              color: textColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer4<WebHomeProviderStatic, ObjectCreationProvider,
        RolesProvider, GoDetailsProvider>(
      builder: (context, provider, objectCreationProvider, rolesProvider,
          goDetailsProvider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // Header with toggle
              // _buildHeader(context, provider),

              // Content area
              Expanded(
                child: _buildContent(
                    context, provider, rolesProvider, goDetailsProvider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        // color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: .5),
        ),
        // boxShadow: [
        //   BoxShadow(
        //     color: Color(0x1A000000), // Black with 10% opacity
        //     blurRadius: 8,
        //     offset: Offset(0, 2),
        //     spreadRadius: 0,
        //   ),
        // ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Dynamic header label based on toggle state
          Text(
            // provider.isAIMode ? 'Objects' : 'Extracted Details',
            'Extracted Details',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleSmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.bold,
              height: 1,
            ),
          ),

          // Right side with toggle and manually process text
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // AI/Manual Toggle
              _buildAIManualToggle(context, provider),

              const SizedBox(width: 16),
              // // Manually Process text
              // Text(
              //   'Manually Process',
              //   style: FontManager.getCustomStyle(
              //     fontSize: ResponsiveFontSizes.titleSmall(context),
              //     color: Colors.white,
              //     fontWeight: FontWeight.w400,
              //     fontFamily: FontManager.fontFamilyTiemposText,
              //     height: 1,
              //   ),
              // ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAIManualToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Toggle switch
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              // Close entity details panel if it's open before switching modes
              final manualProvider =
                  Provider.of<ManualCreationProvider>(context, listen: false);
              if (manualProvider.selectedEntity != null) {
                manualProvider.setSelectedEntity(null);
              }

              provider.toggleAIMode();
              manualProvider.handleEntityValidationForBook();
            },
            child: Container(
              width: 34,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment: provider.isAIMode
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(width: 4),

        // ai label on the right
        Text(
          'Form',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.black,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
        const SizedBox(width: 12),
        // manua; label on the left
        Text(
          'Manual Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.black,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider,
      RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
          physics: const AlwaysScrollableScrollPhysics(),
        ),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final screenHeight = MediaQuery.of(context).size.height;
            final availableHeight =
                screenHeight - 180; // Account for header and padding

            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  // FIXED: Use dynamic height based on content but ensure minimum
                  minHeight: availableHeight,
                  // Allow unlimited height for overflow content
                  maxHeight: double.infinity,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Main content - this will expand as needed
                      Flexible(
                        fit: FlexFit.loose,
                        child: _buildContentWithLineNumbers(
                            context, rolesProvider, goDetailsProvider),
                      ),
                      // Bottom spacing for dropdowns
                      const SizedBox(height: 300),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEntityFirstRow(BuildContext context,
      MyLibraryProvider libraryProvider, GoDetailsProvider goDetailsProvider) {
    return Row(
      children: [
        // Solution text (clickable to edit) - ONLY for solution field
        Expanded(
          flex: 5, // Same flex as the solution text field in edit mode
          child: MouseRegion(
            cursor: SystemMouseCursors.click,
            child: InkWell(
              onTap: () {
                goDetailsProvider.updateEditMode(true);
              },
              child: Text(
                'Solution: ${goDetailsProvider.solutionController.text.isEmpty ? 'Not specified' : goDetailsProvider.solutionController.text}',
                style: TextStyle(
                    fontSize: ResponsiveFontSizes.labelMedium(context),
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText),
              ),
            ),
          ),
        ),
        const SizedBox(width: AppSpacing.xs), // Same spacing as edit mode

        // Agent Type - Keep it exactly same as in edit mode
        Text(
          'Agent Type:',
          style: TextStyle(
              fontSize: ResponsiveFontSizes.labelMedium(context),
              fontWeight: FontWeight.w700,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText),
        ),
        SizedBox(width: AppSpacing.xxs), // Same spacing as edit mode
        Expanded(
          flex: 1, // Same flex as in edit mode
          child: Consumer<MyLibraryProvider>(
            builder: (context, libraryProvider, child) {
              return _buildAgentTypeDropdown(
                  context, libraryProvider, goDetailsProvider);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildContentWithLineNumbers(BuildContext context,
      RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    int lineNumber = 1;
    final List<Widget> allWidgets = [];
    if (goDetailsProvider.showLocalObjectiveDetails) {
      return ExtractLoDetailsMiddleStatic();
    }

    // Edit mode logic - similar to _InnerEntityScreenState
    if (goDetailsProvider.isEditMode) {
      // Line 1: Solution row (editable)
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, Consumer<MyLibraryProvider>(
        builder: (context, libraryProvider, child) {
          return _buildFirstRow(context, libraryProvider, goDetailsProvider);
        },
      )));
      allWidgets.add(const SizedBox(height: 16));

      // Line 2: Description row (editable)
      allWidgets.add(_buildLineWithNumber(
          lineNumber++, _buildSecondRow(context, goDetailsProvider)));
    } else {
      // Show read-only version
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, Consumer<MyLibraryProvider>(
        builder: (context, libraryProvider, child) {
          return _buildEntityFirstRow(
              context, libraryProvider, goDetailsProvider);
        },
      )));
    }

    // Show local objectives section after validation
    if (goDetailsProvider.currentStep == GoDetailsStep.afterValidation ||
        goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) {
      allWidgets.add(const SizedBox(height: 24));

      // Line 3: LOCAL OBJECTIVES header
      allWidgets.add(
          _buildLineWithNumber(lineNumber++, _buildLocalObjectivesHeader()));
      allWidgets.add(const SizedBox(height: 8));

      // Local objectives content with line numbers
      if (goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) {
        // Show LO list with line numbers
        for (final entry in goDetailsProvider
            .currentGoModel!.localObjectivesList!
            .asMap()
            .entries) {
          final index = entry.key;
          final objective = entry.value;

          // Add spacing before each LO (except the first one)
          if (index > 0) {
            allWidgets.add(const SizedBox(height: 12));
          }

          // Add LO item
          allWidgets.add(_buildLineWithNumber(
            lineNumber++,
            _buildLocalObjectiveItem(
                index, objective.name ?? '', goDetailsProvider),
          ));

          // Add LO insertion text field if open for this LO
          if (goDetailsProvider.isLoInsertionOpen(index)) {
            allWidgets.add(_buildLineWithNumber(
              lineNumber++,
              _buildLoInsertionField(index, goDetailsProvider),
            ));
          }

          // Add pathway creation fields if open for this LO
          if (goDetailsProvider.isPathwayCreationOpen(index)) {
            final selectedType =
                goDetailsProvider.getPathwaySelectedType(index);

            if (selectedType == 'Alternative' || selectedType == 'Parallel') {
              // Add Apply Condition section with individual line numbers
              List<Widget> conditionWidgets =
                  _buildApplyConditionFieldsWithLineNumbers(
                      index, goDetailsProvider, rolesProvider, lineNumber);

              allWidgets.add(
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Your existing Column content
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: conditionWidgets,
                    ),

                    const SizedBox(height: 8), // Space between

                    // Row for buttons
                    // Row(
                    //   mainAxisAlignment: MainAxisAlignment.end,
                    //   children: [
                    //     MouseRegion(
                    //       cursor: SystemMouseCursors.click, // Pointer cursor
                    //       child: Container(
                    //         padding: const EdgeInsets.all(8.0),
                    //         decoration: BoxDecoration(
                    //           border: Border.all(color: Color(0xFF707070)),
                    //           borderRadius: BorderRadius.circular(2),
                    //         ),
                    //         child: const Text(
                    //           "Cancel",
                    //           style: TextStyle(
                    //             fontSize: 10,
                    //             fontWeight: FontWeight.w400,
                    //             color: Colors.black,
                    //             fontFamily: FontManager.fontFamilyTiemposText,
                    //           ),
                    //         ),
                    //       ),
                    //     ),
                    //     const SizedBox(width: 8),
                    //     MouseRegion(
                    //       cursor: SystemMouseCursors.click, // Pointer cursor
                    //       child: Container(
                    //         padding: const EdgeInsets.all(8.0),
                    //         decoration: BoxDecoration(
                    //           border: Border.all(color: Color(0xFF707070)),
                    //           borderRadius: BorderRadius.circular(2),
                    //         ),
                    //         child: const Text(
                    //           "Validate",
                    //           style: TextStyle(
                    //             fontSize: 10,
                    //             fontWeight: FontWeight.w400,
                    //             color: Colors.black,
                    //             fontFamily: FontManager.fontFamilyTiemposText,
                    //           ),
                    //         ),
                    //       ),
                    //     ),
                    //   ],
                    // ),
                  ],
                ),
              );
              lineNumber +=
                  4; // 4 rows: header + first condition + second condition + +LO
            } else if (selectedType == 'Sequential' ||
                selectedType == 'Recursive' ||
                selectedType == 'Terminal') {
              // Add blue container with individual line numbers for Sequential, Recursive, Terminal
              allWidgets.addAll(
                  _buildSequentialRecursiveTerminalFieldsWithLineNumbers(
                      index, goDetailsProvider, rolesProvider, lineNumber));
              lineNumber += 1; // 1 row for these types
            } else {
              // For initial state (no type selected) or any other types - wrap in blue container
              allWidgets.addAll(
                  _buildSequentialRecursiveTerminalFieldsWithLineNumbers(
                      index, goDetailsProvider, rolesProvider, lineNumber));
              lineNumber += 1; // 1 row for initial fields
            }
          }
        }
      } else {
        // Line 4: Input field
        allWidgets.add(_buildLineWithNumber(lineNumber++,
            _buildLocalObjectiveInput(context, goDetailsProvider)));
      }
    }
    List<String> generatdPatway = goDetailsProvider.generatedPathways();
    generatdPatway.isNotEmpty
        ? allWidgets.add(
            generatePathWayWidgetWithLineNumber(generatdPatway, lineNumber))
        : SizedBox();

    // Add responsive spacing at the end to extend the vertical line
    final screenHeight = MediaQuery.of(context).size.height;
    final dynamicSpacing = screenHeight * 0.8; // 50% of screen height
    allWidgets.add(SizedBox(height: dynamicSpacing));

    return Stack(
      clipBehavior: Clip.none,
      children: [
        // Content
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: allWidgets,
        ),
        // Continuous vertical line - positioned to extend through all content
        Positioned(
          left: 28, // Position after line number (20px width + 8px margin)
          top: 0,
          bottom: 0,
          child: Container(
            width: 1,
            color: Colors.grey.shade300,
          ),
        ),
      ],
    );
  } // Widget _buildContentWithLineNumbers(BuildContext context,
  //     RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
  //   int lineNumber = 1;
  //   final List<Widget> allWidgets = [];
  //   if (goDetailsProvider.showLocalObjectiveDetails) {
  //     return ExtractLoDetailsMiddleStatic();
  //   }

  //   // Line 1: Solution row
  //   allWidgets
  //       .add(_buildLineWithNumber(lineNumber++, Consumer<MyLibraryProvider>(
  //     builder: (context, libraryProvider, child) {
  //       return _buildFirstRow(context, libraryProvider, goDetailsProvider);
  //     },
  //   )));
  //   allWidgets.add(const SizedBox(height: 16));

  //   // Line 2: Description row
  //   allWidgets.add(_buildLineWithNumber(
  //       lineNumber++, _buildSecondRow(context, goDetailsProvider)));

  //   // Show local objectives section after validation
  //   if (goDetailsProvider.currentStep == GoDetailsStep.afterValidation ||
  //       goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) {
  //     allWidgets.add(const SizedBox(height: 24));

  //     // Line 3: LOCAL OBJECTIVES header
  //     allWidgets.add(
  //         _buildLineWithNumber(lineNumber++, _buildLocalObjectivesHeader()));
  //     allWidgets.add(const SizedBox(height: 8));

  //     // Local objectives content with line numbers
  //     if (goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) {
  //       // Show LO list with line numbers
  //       for (final entry in goDetailsProvider
  //           .currentGoModel!.localObjectivesList!
  //           .asMap()
  //           .entries) {
  //         final index = entry.key;
  //         final objective = entry.value;

  //         // Add spacing before each LO (except the first one)
  //         if (index > 0) {
  //           allWidgets.add(const SizedBox(height: 12));
  //         }

  //         // Add LO item
  //         allWidgets.add(_buildLineWithNumber(
  //           lineNumber++,
  //           _buildLocalObjectiveItem(
  //               index, objective.name ?? '', goDetailsProvider),
  //         ));

  //         // Add LO insertion text field if open for this LO
  //         if (goDetailsProvider.isLoInsertionOpen(index)) {
  //           allWidgets.add(_buildLineWithNumber(
  //             lineNumber++,
  //             _buildLoInsertionField(index, goDetailsProvider),
  //           ));
  //         }

  //         // Add pathway creation fields if open for this LO
  //         if (goDetailsProvider.isPathwayCreationOpen(index)) {
  //           final selectedType =
  //               goDetailsProvider.getPathwaySelectedType(index);

  //           if (selectedType == 'Alternative' || selectedType == 'Parallel') {
  //             // Add Apply Condition section with individual line numbers
  //             List<Widget> conditionWidgets =
  //                 _buildApplyConditionFieldsWithLineNumbers(
  //                     index, goDetailsProvider, rolesProvider, lineNumber);

  //             allWidgets.add(
  //               Column(
  //                 crossAxisAlignment: CrossAxisAlignment.start,
  //                 children: conditionWidgets,
  //               ),
  //             );
  //             lineNumber +=
  //                 4; // 4 rows: header + first condition + second condition + +LO
  //           } else if (selectedType == 'Sequential' ||
  //               selectedType == 'Recursive' ||
  //               selectedType == 'Terminal') {
  //             // Add blue container with individual line numbers for Sequential, Recursive, Terminal
  //             allWidgets.addAll(
  //                 _buildSequentialRecursiveTerminalFieldsWithLineNumbers(
  //                     index, goDetailsProvider, rolesProvider, lineNumber));
  //             lineNumber += 1; // 1 row for these types
  //           } else {
  //             // For initial state (no type selected) or any other types - wrap in blue container
  //             allWidgets.addAll(_buildInitialPathwayFieldsWithLineNumbers(
  //                 index, goDetailsProvider, rolesProvider, lineNumber));
  //             lineNumber += 1; // 1 row for initial fields
  //           }
  //         }
  //       }
  //     } else {
  //       // Line 4: Input field
  //       allWidgets.add(_buildLineWithNumber(lineNumber++,
  //           _buildLocalObjectiveInput(context, goDetailsProvider)));
  //     }
  //   }

  //   // Add responsive spacing at the end to extend the vertical line
  //   final screenHeight = MediaQuery.of(context).size.height;
  //   final dynamicSpacing = screenHeight * 0.8; // 50% of screen height
  //   allWidgets.add(SizedBox(height: dynamicSpacing));

  //   return Stack(
  //     children: [
  //       // Content
  //       Column(
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: allWidgets,
  //       ),
  //       // Continuous vertical line - positioned to extend through all content
  //       Positioned(
  //         left: 28, // Position after line number (20px width + 8px margin)
  //         top: 0,
  //         bottom: 0,
  //         child: Container(
  //           width: 1,
  //           color: Colors.grey.shade300,
  //         ),
  //       ),
  //     ],
  //   );
  // }

  List<Widget> _buildApplyConditionFieldsWithLineNumbers(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;
    final dropdownWidth = _getDropdownWidth(context);
    final dropdownAttributeWidth = _getAttributeDropdownWidth(context);

    // Row 1: Apply Condition header with line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Material(
        elevation: 1, // Low elevation to stay below GIF (elevation 20)
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(4),
          topRight: Radius.circular(4),
        ),
        child: Container(
          width: double.infinity, // This ensures full width
          decoration: const BoxDecoration(
            color: Color(0xFFF7F7F7),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(4),
              topRight: Radius.circular(4),
            ),
          ),
          padding: const EdgeInsets.all(8),
          child: const Center(
            child: Text(
              'Apply Condition',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
        ),
      ),
    ));

    // Row 2: Role and Type dropdowns with first pathway entry
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Material(
        elevation: 1, // Low elevation to stay below GIF (elevation 20)
        child: Container(
          width: double.infinity, // Full width
          decoration: const BoxDecoration(
            color: Color(0xFFF7F7F7),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Use Expanded to make dropdowns take available space
              Expanded(
                flex: 2,
                child: Consumer<MyLibraryProvider>(
                  builder: (context, libraryProvider, child) {
                    return _buildPathwayRoleField(
                        loIndex, goDetailsProvider, libraryProvider);
                  },
                ),
              ),
              const SizedBox(width: 8),

              Expanded(
                flex: 2,
                child: _buildPathwayTypeField(loIndex, goDetailsProvider),
              ),
              const SizedBox(width: 8),

              Expanded(
                flex: 2,
                child: _buildDynamicLODropdown(loIndex, 0, goDetailsProvider),
              ),
              const SizedBox(width: 16),

              Expanded(
                flex: 3,
                child: _buildDynamicEntityAttributeDropdown(
                  loIndex: loIndex,
                  entryIndex: 0,
                  isAfterCondition: false,
                  goDetailsProvider: goDetailsProvider,
                ),
              ),
              const SizedBox(width: 8),

              Expanded(
                flex: 2,
                child: _buildDynamicConditionDropdown(
                  loIndex: loIndex,
                  entryIndex: 0,
                  goDetailsProvider: goDetailsProvider,
                ),
              ),
              const SizedBox(width: 8),

              Expanded(
                flex: 3,
                child: _buildDynamicEntityAttributeDropdown(
                  loIndex: loIndex,
                  entryIndex: 0,
                  isAfterCondition: true,
                  goDetailsProvider: goDetailsProvider,
                ),
              ),
            ],
          ),
        ),
      ),
    ));

    // Dynamic pathway entry rows (starting from index 1)
    final pathwayEntries = goDetailsProvider.getPathwayEntries(loIndex);
    final entryCount = pathwayEntries.length;

    if (entryCount == 0) {
      goDetailsProvider.addPathwayEntry(loIndex);
      goDetailsProvider.addPathwayEntry(loIndex);
    } else if (entryCount == 1) {
      goDetailsProvider.addPathwayEntry(loIndex);
    }

    final updatedEntryCount =
        goDetailsProvider.getPathwayEntries(loIndex).length;
    for (int entryIndex = 1;
        entryIndex < math.max(2, updatedEntryCount);
        entryIndex++) {
      widgets.add(_buildLineWithNumber(
        currentLineNumber++,
        Material(
          elevation: 1, // Low elevation to stay below GIF (elevation 20)
          child: Container(
            width: double.infinity, // Full width
            decoration: const BoxDecoration(
              color: Color(0xFFF7F7F7),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            child: Row(
              children: [
                // Empty space to align with dropdowns above
                Expanded(flex: 2, child: Container()),
                const SizedBox(width: 8),
                // Expanded(flex: 2, child: Container()),
                // const SizedBox(width: 8),
                Expanded(
                  flex: 2,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // Remove button positioned on the left within the Function Type column space
                      InkWell(
                        onTap: () {
                          goDetailsProvider.removePathwayEntry(
                              loIndex, entryIndex);
                        },
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          child: const Icon(
                            Icons.close,
                            size: 16,
                            color: Colors.red,
                          ),
                        ),
                      ),
                      // Rest of the space remains empty to match Function Type column width
                      // Expanded(child: Container()),
                    ],
                  ),
                ),

                const SizedBox(width: 8),
                Expanded(
                  flex: 2,
                  child: _buildDynamicLODropdown(
                      loIndex, entryIndex, goDetailsProvider),
                ),
                const SizedBox(width: 16),

                Expanded(
                  flex: 3,
                  child: _buildDynamicEntityAttributeDropdown(
                    loIndex: loIndex,
                    entryIndex: entryIndex,
                    isAfterCondition: false,
                    goDetailsProvider: goDetailsProvider,
                  ),
                ),
                const SizedBox(width: 8),

                Expanded(
                  flex: 2,
                  child: _buildDynamicConditionDropdown(
                    loIndex: loIndex,
                    entryIndex: entryIndex,
                    goDetailsProvider: goDetailsProvider,
                  ),
                ),
                const SizedBox(width: 8),

                Expanded(
                  flex: 3,
                  child: _buildDynamicEntityAttributeDropdown(
                    loIndex: loIndex,
                    entryIndex: entryIndex,
                    isAfterCondition: true,
                    goDetailsProvider: goDetailsProvider,
                  ),
                ),
              ],
            ),
          ),
        ),
      ));
    }

    // Add +LO button and control icons in a separate row
    widgets.add(_buildLineWithNumber(
        currentLineNumber++,
        Material(
          elevation: 1, // Low elevation to stay below GIF (elevation 20)
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(4),
            bottomRight: Radius.circular(4),
          ),
          child: Container(
            width: double.infinity, // Full width
            decoration: const BoxDecoration(
              color: Color(0xFFF7F7F7),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(4),
                bottomRight: Radius.circular(4),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            child: Row(
              children: [
                // +LO button on the left
                InkWell(
                  onTap: () {
                    goDetailsProvider.addPathwayEntry(loIndex);
                  },
                  child: const Text(
                    '+ LO',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.blue,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),

                const Spacer(), // Push the control icons to the right

                // Add pathway creation control buttons
                Row(
                  children: [
                    // Cancel button
                    _buildActionButton(
                      text: 'Cancel',
                      onTap: () {
                        // Close the pathway creation and revert to the previous state
                        goDetailsProvider.togglePathwayCreation(loIndex);
                      },
                      backgroundColor: Colors.white,
                      textColor: Colors.black,
                      borderColor: Colors.grey.shade400,
                    ),
                    const SizedBox(width: 8), // Space between buttons

                    // Validate button
                    _buildActionButton(
                      text: 'Validate',
                      onTap: () {
                        // Debug current state before checkmark
                        goDetailsProvider.debugPathwayState(loIndex);

                        // Validate pathway creation before accepting
                        if (!goDetailsProvider
                            .canCompletePathwayCreation(loIndex)) {
                          // Don't close if validation fails
                          Logger.warning(
                              'Pathway creation validation failed for LO-${loIndex + 1}');
                          return;
                        }

                        Logger.info(
                            'Validate clicked for LO-${loIndex + 1} - saving and closing');

                        // Save pathway data to GoModel before closing
                        goDetailsProvider.savePathwayDataToGoModel(loIndex);

                        // Force close pathway creation (don't toggle)
                        goDetailsProvider.closePathwayCreation(loIndex);

                        // Update footer visibility based on current state
                        goDetailsProvider.updateFooterVisibilityBasedOnLOs();

                        // Debug state after checkmark
                        goDetailsProvider.debugPathwayState(loIndex);
                      },
                      backgroundColor: const Color(0xFF007AFF),
                      textColor: Colors.white,
                    ),
                  ],
                ),
              ],
            ),
          ),
        )));

    return widgets;
  } // Build initial pathway fields (Role and Type dropdowns) with blue container and line numbers

  List<Widget> _buildInitialPathwayFieldsWithLineNumbers(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;
    final dropdownWidth = _getDropdownWidth(context);

    // Single row with blue background container and line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Material(
        // elevation: 1, // Low elevation to stay below GIF (elevation 20)
        borderRadius: const BorderRadius.all(Radius.circular(4)),
        child: Container(
          width: double.infinity,
          decoration: const BoxDecoration(
            color:
                Color(0xFFF7F7F7), // Blue background (same as other containers)
            borderRadius: BorderRadius.all(Radius.circular(4)),
          ),
          padding: const EdgeInsets.all(12),
          child: _buildInitialPathwayContent(
              loIndex, goDetailsProvider, rolesProvider, dropdownWidth),
        ),
      ),
    ));

    return widgets;
  }

  // Build content for initial pathway creation (Role and Type dropdowns only)
  Widget _buildInitialPathwayContent(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      double dropdownWidth) {
    return Row(
      children: [
        // Select Role dropdown
        SizedBox(
          width: dropdownWidth,
          child: Consumer<MyLibraryProvider>(
            builder: (context, libraryProvider, child) {
              return _buildPathwayRoleField(
                  loIndex, goDetailsProvider, libraryProvider);
            },
          ),
        ),
        const SizedBox(width: 8),

        // Select Type dropdown
        SizedBox(
          width: dropdownWidth,
          child: _buildPathwayTypeField(loIndex, goDetailsProvider),
        ),

        // Add spacer to push icons to the right
        const Spacer(),

        // Add pathway creation control buttons
        Row(
          children: [
            // Cancel button
            _buildActionButton(
              text: 'Cancel',
              onTap: () {
                // Close the pathway creation and revert to the previous state
                goDetailsProvider.togglePathwayCreation(loIndex);
              },
              backgroundColor: Colors.white,
              textColor: Colors.black,
              borderColor: Colors.grey.shade400,
            ),
            const SizedBox(width: 8), // Space between buttons

            // Validate button
            _buildActionButton(
              text: 'Validate',
              onTap: () {
                // Debug current state before checkmark
                goDetailsProvider.debugPathwayState(loIndex);

                // Validate pathway creation before accepting
                if (!goDetailsProvider.canCompletePathwayCreation(loIndex)) {
                  // Get detailed validation errors
                  final validationErrors =
                      goDetailsProvider.getPathwayValidationErrors(loIndex);
                  final errorMessage = validationErrors.join('\n');

                  // Show error message to user
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Pathway validation failed:\n$errorMessage',
                        style: TextStyle(fontSize: 14),
                      ),
                      backgroundColor: Colors.red,
                      duration: Duration(seconds: 5),
                      behavior: SnackBarBehavior.floating,
                      action: SnackBarAction(
                        label: 'Dismiss',
                        textColor: Colors.white,
                        onPressed: () {
                          ScaffoldMessenger.of(context).hideCurrentSnackBar();
                        },
                      ),
                    ),
                  );

                  // Don't close if validation fails - this prevents state corruption
                  Logger.warning(
                      'Pathway creation validation failed for LO-${loIndex + 1}: $errorMessage');
                  return;
                }

                Logger.info(
                    'Validate clicked for LO-${loIndex + 1} - saving and closing');

                // Save pathway data to GoModel before closing
                goDetailsProvider.savePathwayDataToGoModel(loIndex);

                // Force close pathway creation (don't toggle)
                goDetailsProvider.closePathwayCreation(loIndex);

                // Update footer visibility based on current state
                goDetailsProvider.updateFooterVisibilityBasedOnLOs();

                // Debug state after checkmark
                goDetailsProvider.debugPathwayState(loIndex);
              },
              backgroundColor: const Color(0xFF007AFF),
              textColor: Colors.white,
            ),
          ],
        ),
      ],
    );
  }

  // Build Sequential, Recursive, Terminal fields with individual line numbers and blue container
  List<Widget> _buildSequentialRecursiveTerminalFieldsWithLineNumbers(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;
    final dropdownWidth = _getDropdownWidth(context);

    // Single row with blue background container and line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Material(
        elevation: 1, // Low elevation to stay below GIF (elevation 20)
        borderRadius: const BorderRadius.all(Radius.circular(4)),
        child: Container(
          width: double.infinity,
          decoration: const BoxDecoration(
            color: Color(0xFFF7F7F7),
            borderRadius: BorderRadius.all(Radius.circular(4)),
          ),
          padding: const EdgeInsets.all(12),
          child: _buildSequentialRecursiveTerminalContent(
              loIndex, goDetailsProvider, rolesProvider, dropdownWidth),
        ),
      ),
    ));

    return widgets;
  }

  // Build content for Sequential, Recursive, Terminal types
  Widget _buildSequentialRecursiveTerminalContent(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      double dropdownWidth) {
    final selectedType = goDetailsProvider.getPathwaySelectedType(loIndex);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Select Role dropdown
        SizedBox(
          width: dropdownWidth,
          child: Consumer<MyLibraryProvider>(
            builder: (context, libraryProvider, child) {
              return _buildPathwayRoleField(
                  loIndex, goDetailsProvider, libraryProvider);
            },
          ),
        ),
        const SizedBox(width: 8),

        // Select Type dropdown
        SizedBox(
          width: dropdownWidth,
          child: _buildPathwayTypeField(loIndex, goDetailsProvider),
        ),

        // Third field based on selected type
        if (selectedType == 'Sequential') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: dropdownWidth,
            child: _buildPathwayLOField(loIndex, goDetailsProvider),
          ),
        ] else if (selectedType == 'Recursive') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: dropdownWidth,
            child: _buildRecursiveInputField(loIndex, goDetailsProvider),
          ),
        ],
        // Terminal type has no third field
        if (selectedType == 'Terminal') ...[
          const SizedBox(width: 8),
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                border: Border.all(color: Color(0xFFFF9390), width: 0.5)),
            child: Icon(Icons.close, color: Color(0xFFFF211B), size: 14),
          ),
        ],

        // Add spacer to push icons to the right
        const Spacer(),

        // Add pathway creation control buttons
        Row(
          children: [
            // Cancel button
            _buildActionButton(
              text: 'Cancel',
              onTap: () {
                // Close the pathway creation and revert to the previous state
                goDetailsProvider.togglePathwayCreation(loIndex);
              },
              backgroundColor: Colors.white,
              textColor: Colors.black,
              borderColor: Colors.grey.shade400,
            ),
            const SizedBox(width: 8), // Space between buttons

            // Validate button
            _buildActionButton(
              text: 'Validate',
              onTap: () {
                // Debug current state before checkmark
                goDetailsProvider.debugPathwayState(loIndex);

                // Validate pathway creation before accepting
                if (!goDetailsProvider.canCompletePathwayCreation(loIndex)) {
                  // Get detailed validation errors
                  final validationErrors =
                      goDetailsProvider.getPathwayValidationErrors(loIndex);
                  final errorMessage = validationErrors.join('\n');

                  // Show error message to user
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Pathway validation failed:\n$errorMessage',
                        style: TextStyle(fontSize: 14),
                      ),
                      backgroundColor: Colors.red,
                      duration: Duration(seconds: 5),
                      behavior: SnackBarBehavior.floating,
                      action: SnackBarAction(
                        label: 'Dismiss',
                        textColor: Colors.white,
                        onPressed: () {
                          ScaffoldMessenger.of(context).hideCurrentSnackBar();
                        },
                      ),
                    ),
                  );

                  // Don't close if validation fails
                  Logger.warning(
                      'Pathway creation validation failed for LO-${loIndex + 1}: $errorMessage');
                  return;
                }

                Logger.info(
                    'Validate clicked for LO-${loIndex + 1} - saving and closing');

                // Save pathway data to GoModel before closing
                goDetailsProvider.savePathwayDataToGoModel(loIndex);

                // Force close pathway creation (don't toggle)
                goDetailsProvider.closePathwayCreation(loIndex);

                // Update footer visibility based on current state
                goDetailsProvider.updateFooterVisibilityBasedOnLOs();

                // Debug state after checkmark
                goDetailsProvider.debugPathwayState(loIndex);
              },
              backgroundColor: const Color(0xFF007AFF),
              textColor: Colors.white,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFirstRow(BuildContext context, MyLibraryProvider libraryProvider,
      GoDetailsProvider goDetailsProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // Solution label and text field with validation
            Text(
              'Solution:',
              style: TextStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText),
            ),
            const SizedBox(width: AppSpacing.xs),
            Expanded(
              flex: 5, // This flex should match the read-only version
              child: Container(
                height: 35,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: TextField(
                  controller: goDetailsProvider.solutionController,
                  onChanged: (value) {
                    // Clear validation error when user starts typing
                    if (goDetailsProvider.solutionValidationError != null) {
                      goDetailsProvider.clearValidationErrors();
                    }
                  },
                  decoration: const InputDecoration(
                    hintText: 'Type here..',
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    fillColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    contentPadding: EdgeInsets.symmetric(
                        horizontal: 4, vertical: 12), // Remove default padding
                    isDense: true,
                  ),
                  style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText),
                ),
              ),
            ),
            const SizedBox(width: AppSpacing.xs), // Same spacing as read-only
            Text(
              'Select Role:',
              style: TextStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText),
            ),
            SizedBox(width: AppSpacing.xs), // Same spacing as read-only
            Expanded(
              flex: 1, // Same flex as read-only version
              child: Consumer<MyLibraryProvider>(
                builder: (context, libraryProvider, child) {
                  return _buildAgentTypeDropdown(
                      context, libraryProvider, goDetailsProvider);
                },
              ),
            ),
          ],
        ),
        // Validation error message - placed outside the Row to avoid layout issues
        if (goDetailsProvider.solutionValidationError != null)
          Padding(
            padding: EdgeInsets.only(
                left: MediaQuery.of(context).size.width * 0.048,
                top: 2), // Align with text field
            child: Text(
              goDetailsProvider.solutionValidationError!,
              style: const TextStyle(
                fontSize: 8,
                color: Colors.red,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
      ],
    );
  }

  // Widget _buildFirstRow(BuildContext context, MyLibraryProvider libraryProvider,
  //     GoDetailsProvider goDetailsProvider) {
  //   return Row(
  //     children: [
  //       // Solution label and text field with validation
  //       Expanded(
  //         // flex: 3,
  //         child: Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             Row(
  //               children: [
  //                 Text(
  //                   'Solution:',
  //                   style: TextStyle(
  //                       fontSize: ResponsiveFontSizes.labelMedium(context),
  //                       fontWeight: FontWeight.w700,
  //                       color: Colors.black,
  //                       fontFamily: FontManager.fontFamilyTiemposText),
  //                 ),
  //                 const SizedBox(width: 28),
  //                 Expanded(
  //                   flex: 8,
  //                   child: Container(
  //                     height: 35,
  //                     decoration: BoxDecoration(
  //                       border: Border.all(color: Colors.grey.shade300),
  //                       borderRadius: BorderRadius.circular(2),
  //                     ),
  //                     child: TextField(
  //                       controller: goDetailsProvider.solutionController,
  //                       onChanged: (value) {
  //                         // Clear validation error when user starts typing
  //                         if (goDetailsProvider.solutionValidationError !=
  //                             null) {
  //                           goDetailsProvider.clearValidationErrors();
  //                         }
  //                       },
  //                       decoration: const InputDecoration(
  //                         hintText: 'Type here..',
  //                         border: InputBorder.none,
  //                         enabledBorder: InputBorder.none,
  //                         focusedBorder: InputBorder.none,
  //                         fillColor: Colors.transparent,
  //                         hoverColor: Colors.transparent,
  //                         contentPadding: EdgeInsets.symmetric(
  //                             horizontal: 4,
  //                             vertical: 12), // Remove default padding
  //                         isDense: true,
  //                       ),
  //                       style: TextStyle(
  //                           fontSize: 10,
  //                           fontWeight: FontWeight.w500,
  //                           color: Colors.black,
  //                           fontFamily: FontManager.fontFamilyTiemposText),
  //                     ),
  //                   ),
  //                 ),
  //                 const SizedBox(width: AppSpacing.xs),
  //                 Text(
  //                   'Agent Type:',
  //                   style: TextStyle(
  //                      fontSize: ResponsiveFontSizes.labelMedium(context),
  //                       fontWeight: FontWeight.w700,
  //                       color: Colors.black,
  //                       fontFamily: FontManager.fontFamilyTiemposText),
  //                 ),

  //                 // Tick icon button
  //                 // InkWell(
  //                 //   onTap: goDetailsProvider.isValidating
  //                 //       ? null
  //                 //       : () async {
  //                 //           // First validate the solution field
  //                 //           if (!goDetailsProvider.validateSolutionField()) {
  //                 //             return; // Stop if validation fails
  //                 //           }

  //                 //           // Create GoModel with collected data before validation
  //                 //           goDetailsProvider.currentGoModel =
  //                 //               goDetailsProvider.createGoModelFromControllers(
  //                 //                   goDetailsProvider);

  //                 //           // Pass the GoModel to validation
  //                 //           await goDetailsProvider.validateSolutionWithGoModel(
  //                 //               goDetailsProvider.currentGoModel);
  //                 //         },
  //                 //   child: Container(
  //                 //     width: 24,
  //                 //     height: 24,
  //                 //     decoration: BoxDecoration(
  //                 //       color: const Color(0xFF007AFF),
  //                 //       shape: BoxShape.circle,
  //                 //     ),
  //                 //     child: const Icon(
  //                 //       Icons.check,
  //                 //       color: Colors.white,
  //                 //       size: 14,
  //                 //     ),
  //                 //   ),
  //                 // ),

  //                 SizedBox(width: AppSpacing.xs),
  //                 Expanded(
  //                   flex: 1,
  //                   child: Consumer<MyLibraryProvider>(
  //                     builder: (context, libraryProvider, child) {
  //                       return _buildAgentTypeDropdown(
  //                           context, libraryProvider, goDetailsProvider);
  //                     },
  //                   ),
  //                 ),
  //               ],
  //             ), // Validation error message
  //             if (goDetailsProvider.solutionValidationError != null)
  //               Padding(
  //                 padding: EdgeInsets.only(
  //                     left: MediaQuery.of(context).size.width * 0.048,
  //                     top: 2), // Align with text field
  //                 child: Text(
  //                   goDetailsProvider.solutionValidationError!,
  //                   style: const TextStyle(
  //                     fontSize: 8,
  //                     color: Colors.red,
  //                     fontFamily: FontManager.fontFamilyTiemposText,
  //                   ),
  //                 ),
  //               ),
  //           ],
  //         ),
  //       ),
  //     ],
  //   );
  // }

  Widget _buildAgentTypeDropdown(BuildContext context,
      MyLibraryProvider libraryProvider, GoDetailsProvider goDetailsProvider) {
    // Fetch library data if not already loaded
    // if (!libraryProvider.isLoading && libraryProvider.roles.isEmpty) {
    //   WidgetsBinding.instance.addPostFrameCallback((_) {
    //     libraryProvider.fetchLibraryData();
    //   });
    // }

    return Row(
      children: [
        Expanded(
          child: SizedBox(
            height: 35,
            child: CustomDropdownWidget(
              label: 'Role',
              list: libraryProvider.isLoading
                  ? ['Loading roles...']
                  : libraryProvider.roles.isEmpty
                      ? ['No roles available']
                      : libraryProvider.getRoleNames(),
              value: goDetailsProvider.selectedRoles
                  .map((role) => role.name ?? '')
                  .toList(),
              isMultiSelect: true,
              onChanged: (selectedValues) {
                if (!libraryProvider.isLoading &&
                    selectedValues is List<String>) {
                  // Filter out loading/error messages
                  final validRoleNames = selectedValues
                      .where((value) =>
                          value != 'Loading roles...' &&
                          value != 'No roles available')
                      .toList();

                  // Convert role names to PostgresRole objects
                  final selectedRoles = <PostgresRole>[];
                  for (final roleName in validRoleNames) {
                    final selectedLibraryRole =
                        libraryProvider.getRoleByName(roleName);
                    if (selectedLibraryRole != null) {
                      final postgresRole = PostgresRole(
                        roleId: selectedLibraryRole.roleId,
                        name: selectedLibraryRole.name,
                        description: selectedLibraryRole.description,
                        tenantId: null,
                        createdAt: selectedLibraryRole.createdAt,
                        updatedAt: selectedLibraryRole.updatedAt,
                      );
                      selectedRoles.add(postgresRole);
                    }
                  }

                  goDetailsProvider.setSelectedRoles(selectedRoles);
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSecondRow(
      BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Text(
              'Description:',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w700,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
            const SizedBox(width: AppSpacing.xs),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      height: 35,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: TextField(
                        controller: goDetailsProvider.descriptionController,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          isDense: true,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          fillColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 4, vertical: 12),
                          hintText: 'Enter description..',
                        ),
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Align(
          alignment: Alignment.centerRight,
          child: InkWell(
            onTap: goDetailsProvider.isValidating
                ? null
                : () async {
                    // First validate the solution field
                    if (!goDetailsProvider.validateSolutionField()) {
                      return; // Stop if validation fails
                    }

                    // Store current step to preserve LO visibility
                    final currentStep = goDetailsProvider.currentStep;
                    final hasExistingLOs = goDetailsProvider
                            .currentGoModel?.localObjectivesList?.isNotEmpty ??
                        false;

                    // Create GoModel with collected data before validation
                    goDetailsProvider.currentGoModel = goDetailsProvider
                        .createGoModelFromControllers(goDetailsProvider);

                    // Pass the GoModel to validation
                    await goDetailsProvider.validateSolutionWithGoModel(
                        goDetailsProvider.currentGoModel);

                    // FIXED: Preserve step if there were existing LOs to keep them visible
                    if (hasExistingLOs &&
                        currentStep == GoDetailsStep.afterLocalObjectives) {
                      goDetailsProvider
                          .setCurrentStep(GoDetailsStep.afterLocalObjectives);
                    }

                    // ADDED: Switch to read-only mode after validation
                    goDetailsProvider.updateEditMode(false);
                  },
            borderRadius: BorderRadius.circular(2),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(2),
              ),
              child: const Text(
                'Validate',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
          ),
        )
      ],
    );
  }

  // Widget _buildSecondRow(
  //     BuildContext context, GoDetailsProvider goDetailsProvider) {
  //   return Column(
  //     children: [
  //       Row(
  //         crossAxisAlignment: CrossAxisAlignment.center,
  //         children: [
  //           const Text(
  //             'Description:',
  //             style: TextStyle(
  //               fontSize: 12,
  //               fontWeight: FontWeight.w700,
  //               color: Colors.black,
  //               fontFamily: FontManager.fontFamilyTiemposText,
  //             ),
  //           ),
  //           const SizedBox(width: AppSpacing.xs),
  //           Expanded(
  //             child: Row(
  //               children: [
  //                 Expanded(
  //                   child: Container(
  //                     height: 35,
  //                     decoration: BoxDecoration(
  //                       border: Border.all(color: Colors.grey.shade300),
  //                       borderRadius: BorderRadius.circular(2),
  //                     ),
  //                     child: TextField(
  //                       controller: goDetailsProvider.descriptionController,
  //                       decoration: const InputDecoration(
  //                         border: InputBorder.none,
  //                         isDense: true,
  //                         enabledBorder: InputBorder.none,
  //                         focusedBorder: InputBorder.none,
  //                         fillColor: Colors.transparent,
  //                         hoverColor: Colors.transparent,
  //                         contentPadding:
  //                             EdgeInsets.symmetric(horizontal: 4, vertical: 12),
  //                         hintText: 'Enter description..',
  //                       ),
  //                       style: TextStyle(
  //                         fontSize: 10,
  //                         fontWeight: FontWeight.w500,
  //                         color: Colors.black,
  //                         fontFamily: FontManager.fontFamilyTiemposText,
  //                       ),
  //                     ),
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ],
  //       ),
  //       const SizedBox(height: 8),
  //       Align(
  //         alignment: Alignment.centerRight,
  //         child: InkWell(
  //           onTap: goDetailsProvider.isValidating
  //               ? null
  //               : () async {
  //                   // First validate the solution field
  //                   if (!goDetailsProvider.validateSolutionField()) {
  //                     return; // Stop if validation fails
  //                   }

  //                   // Create GoModel with collected data before validation
  //                   goDetailsProvider.currentGoModel = goDetailsProvider
  //                       .createGoModelFromControllers(goDetailsProvider);

  //                   // Pass the GoModel to validation
  //                   await goDetailsProvider.validateSolutionWithGoModel(
  //                       goDetailsProvider.currentGoModel);
  //                 },
  //           borderRadius: BorderRadius.circular(2),
  //           child: Container(
  //             padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
  //             decoration: BoxDecoration(
  //               border: Border.all(color: Colors.grey.shade300),
  //               borderRadius: BorderRadius.circular(2),
  //             ),
  //             child: const Text(
  //               'Validate',
  //               style: TextStyle(
  //                 fontSize: 10,
  //                 fontWeight: FontWeight.w400,
  //                 color: Colors.black,
  //                 fontFamily: FontManager.fontFamilyTiemposText,
  //               ),
  //             ),
  //           ),
  //         ),
  //       )
  //     ],
  //   );
  // }

  // Helper methods for line numbers
  Widget _buildLineWithNumber(int lineNumber, Widget content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Line number
        SizedBox(
          // color: Color(0xFFEDF3FF),
          width: 20,
          child: Text(
            '$lineNumber',
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyInter,
            ),
          ),
        ),
        // Space for the continuous vertical line
        const SizedBox(width: 17), // 8px margin + 1px line + 8px margin
        // Content
        Expanded(child: content),
      ],
    );
  }

  Widget _buildLocalObjectivesHeader() {
    return Text(
      'LOCAL OBJECTIVES',
      style: TextStyle(
        fontSize: ResponsiveFontSizes.labelMedium(context),
        fontWeight: FontWeight.w700,
        color: Colors.black,
        fontFamily: FontManager.fontFamilyTiemposText,
      ),
    );
  }

  Widget _buildLocalObjectiveItem(
      int index, String objective, GoDetailsProvider goDetailsProvider) {
    // Get the LO object to check if it's from library
    final lo = goDetailsProvider.currentGoModel?.localObjectivesList?[index];
    final isLibraryLo = lo?.workflowSource == 'library';
    final sourceInfo = lo?.workSource;

    // Check if this LO is currently in edit mode
    final isEditing = goDetailsProvider.isLoEditMode(index);

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // LO content section - either text or edit field
        if (isEditing)
          // Show edit field when in edit mode - takes full width except for + icon space
          Expanded(
            child: _buildLoEditField(index, goDetailsProvider),
          )
        else
          // Show normal content when not in edit mode
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Main row with LO text, edit icon, and create pathway
                InkWell(
                  onTap: () {
                    // Check if GO is validated before allowing LO selection
                    final isGoValidated = goDetailsProvider
                            .currentGoModel?.globalObjectives?.isValidated ??
                        false;

                    if (!isGoValidated) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                              'Please validate the GO first before selecting Local Objectives.'),
                          backgroundColor: Colors.orange,
                          duration: Duration(seconds: 3),
                        ),
                      );
                      return;
                    }

                    final loHasPathWay = goDetailsProvider.currentGoModel
                            ?.localObjectivesList?[index].pathwayData !=
                        null;

                    if (!loHasPathWay) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                              'Please create a pathway for this LO before proceeding.'),
                          backgroundColor: Colors.orange,
                          duration: Duration(seconds: 3),
                        ),
                      );
                      return;
                    }

                    // Show local objective details within the same screen
                    goDetailsProvider.setShowLocalObjectiveDetails(index);
                  },
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // LO text
                      MouseRegion(
                        key: _getLoTextKey(index),
                        onEnter: (_) => CustomTooltip.show(
                          context: context,
                          key: _getLoTextKey(index),
                          message:
                              'Please Tap on the LO to Add Object and Attribute and Related Details',
                          position: TooltipPosition.bottom,
                        ),
                        onExit: (_) => CustomTooltip.hide(),
                        child: Text(
                          'LO-${index + 1}. $objective',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                            fontFamily: FontManager.fontFamilyTiemposText,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Edit icon
                      InkWell(
                        onTap: () {
                          // Prevent event bubbling to parent InkWell
                          // Open edit mode for this LO
                          goDetailsProvider.toggleLoEditMode(index);
                        },
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.edit,
                            color: Colors.grey,
                            size: 10,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Delete icon
                      InkWell(
                        onTap: () {
                          // Show confirmation dialog before deleting
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return AlertDialog(
                                title: const Text('Delete Local Objective'),
                                content: Text(
                                    'Are you sure you want to delete LO-${index + 1}. $objective? This action cannot be undone.'),
                                actions: [
                                  TextButton(
                                    onPressed: () {
                                      Navigator.of(context)
                                          .pop(); // Close dialog
                                    },
                                    child: const Text('Cancel'),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      Navigator.of(context)
                                          .pop(); // Close dialog
                                      // Delete this local objective with comprehensive cleanup
                                      goDetailsProvider
                                          .removeLocalObjective(index);
                                    },
                                    style: TextButton.styleFrom(
                                      foregroundColor: Colors.red,
                                    ),
                                    child: const Text('Delete'),
                                  ),
                                ],
                              );
                            },
                          );
                        },
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: Colors.red.shade100,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.delete,
                            color: Colors.red,
                            size: 10,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppSpacing.sm),
                      // Create pathway button
                      Stack(
                        clipBehavior: Clip.none,
                        alignment: Alignment.topLeft,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 3),
                            child: MouseRegion(
                              onEnter: (_) {
                                _setPathwayHoverState(index, true);
                              },
                              onExit: (_) {
                                _setPathwayHoverState(index, false);
                              },
                              child: InkWell(
                                key: _getPathwayButtonKey(index),
                                onTap: () {
                                  goDetailsProvider
                                      .togglePathwayCreation(index);
                                },
                                child: const Text(
                                  'Create pathway',
                                  style: TextStyle(
                                    decoration: TextDecoration.underline,
                                    decorationColor: Color(0xFF007AFF),
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                    height: 2.0,
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Show source info if available
                if (isLibraryLo && sourceInfo != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    sourceInfo,
                    style: TextStyle(
                      fontSize: 9,
                      fontWeight: FontWeight.w400,
                      color: Colors.grey[600],
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),

        // Show + icon only when not in edit mode
        if (!isEditing) ...[
          const Spacer(),
          Builder(
            builder: (context) {
              final GlobalKey addLoIconKey = GlobalKey();
              return InkWell(
                onTap: () {
                  goDetailsProvider.toggleLoInsertion(index);
                },
                child: MouseRegion(
                  key: addLoIconKey,
                  hitTestBehavior: HitTestBehavior.translucent,
                  onEnter: (_) => CustomTooltip.show(
                    context: context,
                    key: addLoIconKey,
                    message: 'Please click on the “+” Icon to add another LO.',
                    position: TooltipPosition.left,
                  ),
                  onExit: (_) => CustomTooltip.hide(),
                  child: const Icon(
                    Icons.add,
                    color: Colors.grey,
                    size: 16,
                  ),
                ),
              );
            },
          ),
        ],
      ],
    );
  }

  /// Enhanced validation method for Local Objectives
  bool _validateLocalObjectiveNames(
      String inputText, GoDetailsProvider goDetailsProvider) {
    // Get current GO name
    final goName = goDetailsProvider.solutionController.text.trim();

    // Split input by periods and clean up
    final loNames = inputText
        .split('.')
        .map((name) => name.trim())
        .where((name) => name.isNotEmpty)
        .toList();

    // Check if GO name matches any LO name
    for (final loName in loNames) {
      if (loName.toLowerCase() == goName.toLowerCase()) {
        // Set the error directly on the provider's error property
        goDetailsProvider.loValidationError =
            'GO name and LO name cannot be the same.';
        return false;
      }
    }

    // Check for duplicate LO names (case insensitive)
    final lowerCaseNames = loNames.map((name) => name.toLowerCase()).toList();
    final uniqueNames = lowerCaseNames.toSet();

    if (lowerCaseNames.length != uniqueNames.length) {
      goDetailsProvider.loValidationError =
          'Duplicate LO names are not allowed.';
      return false;
    }

    // Check against existing LO names in the model
    final existingLOs =
        goDetailsProvider.currentGoModel?.localObjectivesList ?? [];
    for (final loName in loNames) {
      final existingNames =
          existingLOs.map((lo) => lo.name?.toLowerCase()).toList();
      if (existingNames.contains(loName.toLowerCase())) {
        goDetailsProvider.loValidationError =
            'LO name "$loName" already exists.';
        return false;
      }
    }

    return true;
  }

  Widget _buildLocalObjectiveInput(
      BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Container(
                height: 35,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: TextField(
                  controller: goDetailsProvider.localObjectiveController,
                  onChanged: (value) {
                    // Clear validation error when user starts typing
                    if (goDetailsProvider.loValidationError != null) {
                      goDetailsProvider.clearValidationErrors();
                    }
                  },
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    fillColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 4, vertical: 12),
                    hintText: 'Type LO name with full stop (.)',
                    hintStyle: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                        fontFamily: FontManager.fontFamilyTiemposText),
                  ),
                  style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText),
                ),
              ),
            ),
            const SizedBox(width: 8),
            // Tick icon for local objective
            InkWell(
              onTap: () {
                // Enhanced validation with GO-LO name check and duplicate LO check
                final inputText =
                    goDetailsProvider.localObjectiveController.text.trim();

                // First validate the LO field is not empty
                if (!goDetailsProvider.validateLocalObjectiveField()) {
                  return; // Stop if basic validation fails
                }

                // Enhanced validation for GO-LO name matching and duplicate LOs
                if (!_validateLocalObjectiveNames(
                    inputText, goDetailsProvider)) {
                  return; // Stop if enhanced validation fails
                }

                goDetailsProvider.processLocalObjectives();
                // Footer visibility is now handled in processLocalObjectives method
              },
              child: Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: Color(0xFF007AFF),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 14,
                ),
              ),
            ),
          ],
        ),
        // Validation error message for LO field
        if (goDetailsProvider.loValidationError != null)
          Padding(
            padding: const EdgeInsets.only(top: 2),
            child: Text(
              goDetailsProvider.loValidationError!,
              style: const TextStyle(
                fontSize: 8,
                color: Colors.red,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildLoEditField(int loIndex, GoDetailsProvider goDetailsProvider) {
    final controller = goDetailsProvider.getLoEditController(loIndex);

    // Fallback: Create a temporary controller if the main one is null
    if (controller == null) {
      // Get current LO name
      final currentLo =
          goDetailsProvider.currentGoModel?.localObjectivesList?[loIndex];
      final currentName = currentLo?.name ?? '';

      // Create a temporary controller with current value
      final tempController = TextEditingController(text: currentName);

      // Schedule disposal after widget is built
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Re-initialize the proper controller
        goDetailsProvider.toggleLoEditMode(loIndex);
        if (!goDetailsProvider.isLoEditMode(loIndex)) {
          goDetailsProvider.toggleLoEditMode(loIndex);
        }
      });

      return Row(
        children: [
          Text(
            'LO-${loIndex + 1}. ',
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
          Expanded(
            child: Container(
              height: 30,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(2),
              ),
              child: TextField(
                controller: tempController,
                autofocus: true,
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  isDense: true,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  fillColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                  hintText: 'Edit LO name...',
                  hintStyle: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey,
                      fontFamily: FontManager.fontFamilyTiemposText),
                ),
                style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText),
              ),
            ),
          ),
          const SizedBox(width: 8),
          // Control buttons
          Row(
            children: [
              // Cancel button
              _buildActionButton(
                text: 'Cancel',
                onTap: () {
                  tempController.dispose();
                  goDetailsProvider.cancelLoEdit(loIndex);
                },
                backgroundColor: Colors.white,
                textColor: Colors.black,
                borderColor: Colors.grey.shade400,
              ),
              const SizedBox(width: 8),
              // Save button
              _buildActionButton(
                text: 'Save',
                onTap: () {
                  // Save manually
                  final newName = tempController.text.trim();
                  if (newName.isNotEmpty &&
                      goDetailsProvider.currentGoModel?.localObjectivesList !=
                          null) {
                    if (loIndex <
                        goDetailsProvider
                            .currentGoModel!.localObjectivesList!.length) {
                      goDetailsProvider.currentGoModel!
                          .localObjectivesList![loIndex].name = newName;
                    }
                  }
                  tempController.dispose();
                  goDetailsProvider.cancelLoEdit(loIndex);
                },
                backgroundColor: const Color(0xFF007AFF),
                textColor: Colors.white,
              ),
            ],
          ),
        ],
      );
    }

    // Normal case with proper controller
    return Row(
      children: [
        Text(
          'LO-${loIndex + 1}. ',
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.black,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        Expanded(
          child: Container(
            height: 30,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(2),
            ),
            child: TextField(
              controller: controller,
              autofocus: true,
              decoration: const InputDecoration(
                border: InputBorder.none,
                isDense: true,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                hintText: 'Edit LO name...',
                hintStyle: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                    fontFamily: FontManager.fontFamilyTiemposText),
              ),
              style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText),
            ),
          ),
        ),
        const SizedBox(width: 8),
        // Control buttons
        Row(
          children: [
            // Cancel button
            _buildActionButton(
              text: 'Cancel',
              onTap: () {
                goDetailsProvider.cancelLoEdit(loIndex);
              },
              backgroundColor: Colors.white,
              textColor: Colors.black,
              borderColor: Colors.grey.shade400,
            ),
            const SizedBox(width: 8),
            // Save button
            _buildActionButton(
              text: 'Save',
              onTap: () {
                goDetailsProvider.saveLoEdit(loIndex);
              },
              backgroundColor: const Color(0xFF007AFF),
              textColor: Colors.white,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLoInsertionField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final controller = goDetailsProvider.getLoInsertionController(loIndex);
    // if (controller == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 10),
        Row(
          children: [
            Expanded(
              child: Container(
                height: 35,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: TextField(
                  controller: controller,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    fillColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 4, vertical: 12),
                    hintText: 'Type LO names with full stop (.)',
                    hintStyle: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey,
                        fontFamily: FontManager.fontFamilyTiemposText),
                  ),
                  style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText),
                ),
              ),
            ),
            const SizedBox(width: 8),
            // Add buttons for inserting LOs
            Row(
              children: [
                // Cancel button
                _buildActionButton(
                  text: 'Cancel',
                  onTap: () {
                    // Close the text field and revert to the previous state
                    goDetailsProvider.toggleLoInsertion(
                        loIndex); // Toggle LO insertion state
                  },
                  backgroundColor: Colors.white,
                  textColor: Colors.black,
                  borderColor: Colors.grey.shade400,
                ),
                const SizedBox(width: 8), // Space between buttons

                // Add button
                _buildActionButton(
                  text: 'Add',
                  onTap: () {
                    if (!goDetailsProvider
                        .validateLocalObjectiveInsertionField()) {
                      return; // Stop if validation fails
                    }
                    goDetailsProvider.processLoInsertion(loIndex);
                    goDetailsProvider.toggleLoInsertion(loIndex);
                    // Update footer visibility based on current state
                    goDetailsProvider.updateFooterVisibilityBasedOnLOs();
                  },
                  backgroundColor: const Color(0xFF007AFF),
                  textColor: Colors.white,
                ),
              ],
            ),
          ],
        ),
        if (goDetailsProvider.loInsertionValidationError != null)
          Padding(
            padding: const EdgeInsets.only(top: 2),
            child: Text(
              goDetailsProvider.loInsertionValidationError!,
              style: const TextStyle(
                fontSize: 8,
                color: Colors.red,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
      ],
    );
  }

  // Helper method to get responsive dropdown width
  double _getDropdownWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth <= 1366) {
      return 100.0;
    } else {
      return (screenWidth * 100) / 1366; // Scale proportionally
    }
  }

  double _getAttributeDropdownWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth <= 1366) {
      return 160.0;
    } else {
      return (screenWidth * 160) / 1366; // Scale proportionally
    }
  }

  Widget _buildPathwayRoleField(int loIndex,
      GoDetailsProvider goDetailsProvider, MyLibraryProvider libraryProvider) {
    // Fetch library data if not already loaded
    // if (!libraryProvider.isLoading && libraryProvider.roles.isEmpty) {
    //   WidgetsBinding.instance.addPostFrameCallback((_) {
    //     libraryProvider.fetchLibraryData();
    //   });
    // }

    // Ensure pathway data restoration when building the role field
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // First ensure general pathway data restoration
      goDetailsProvider.ensurePathwayDataRestoration();

      // Then handle role-specific restoration if needed
      if (!libraryProvider.isLoading &&
          libraryProvider.roles.isNotEmpty &&
          goDetailsProvider.hasPendingRoleRestoration(loIndex)) {
        // Debug current state before restoration
        goDetailsProvider.debugPathwayState(loIndex);

        // Convert library roles to PostgresRole objects for restoration
        final availableRoles = libraryProvider.roles
            .map((libRole) => PostgresRole(
                  roleId: libRole.roleId,
                  name: libRole.name,
                  description: libRole.description,
                  tenantId: null,
                  createdAt: libRole.createdAt,
                  updatedAt: libRole.updatedAt,
                ))
            .toList();

        goDetailsProvider.restorePendingRoles(loIndex, availableRoles);

        // Debug state after restoration
        goDetailsProvider.debugPathwayState(loIndex);
      }

      // Auto-sync with Agent Type selection if pathway roles are empty
      if (!libraryProvider.isLoading &&
          libraryProvider.roles.isNotEmpty &&
          goDetailsProvider.getPathwaySelectedMultipleRoles(loIndex).isEmpty &&
          goDetailsProvider.selectedRoles.isNotEmpty) {
        // Sync Agent Type roles to pathway roles
        goDetailsProvider.setPathwaySelectedMultipleRoles(
            loIndex, goDetailsProvider.selectedRoles);
      }
    });

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select Role',
        list: libraryProvider.isLoading
            ? ['Loading roles...']
            : libraryProvider.roles.isEmpty
                ? ['No roles available']
                : libraryProvider.getRoleNames(),
        value: goDetailsProvider
            .getPathwaySelectedMultipleRoles(loIndex)
            .map((role) => role.name ?? '')
            .toList(),
        isMultiSelect: true,
        onChanged: (selectedValues) {
          if (!libraryProvider.isLoading && selectedValues is List<String>) {
            // Filter out loading/error messages
            final validRoleNames = selectedValues
                .where((value) =>
                    value != 'Loading roles...' &&
                    value != 'No roles available')
                .toList();

            // Convert role names to PostgresRole objects
            final selectedRoles = <PostgresRole>[];
            for (final roleName in validRoleNames) {
              final selectedLibraryRole =
                  libraryProvider.getRoleByName(roleName);
              if (selectedLibraryRole != null) {
                final postgresRole = PostgresRole(
                  roleId: selectedLibraryRole.roleId,
                  name: selectedLibraryRole.name,
                  description: selectedLibraryRole.description,
                  tenantId: null,
                  createdAt: selectedLibraryRole.createdAt,
                  updatedAt: selectedLibraryRole.updatedAt,
                );
                selectedRoles.add(postgresRole);
              }
            }

            goDetailsProvider.setPathwaySelectedMultipleRoles(
                loIndex, selectedRoles);
          }
        },
      ),
    );
  }

  Widget _buildPathwayTypeField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final typeOptions = [
      'Sequential',
      'Alternative',
      'Parallel',
      'Recursive',
      'Terminal'
    ];

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select Type',
        list: typeOptions,
        value: goDetailsProvider.getPathwaySelectedType(loIndex),
        onChanged: (selectedType) {
          goDetailsProvider.setPathwaySelectedType(loIndex, selectedType);
        },
      ),
    );
  }

  Widget _buildPathwayLOField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final availableLOs =
        goDetailsProvider.getAvailableLOsForSequential(loIndex);

    // Check if LOs are missing and show error message
    if (availableLOs.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 24,
            child: CustomDropdownWidget(
              label: 'Select LO',
              list: ['No LOs available'],
              value: null,
              onChanged: (value) {
                // Do nothing when no LOs are available
              },
            ),
          ),
          // Error message for missing LOs
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: SizedBox(
              width: double.infinity,
              child: Text(
                'LO is not available',
                textAlign: TextAlign.left,
                style: const TextStyle(
                  fontSize: 8,
                  color: Colors.red,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
          ),
        ],
      );
    }

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select LO',
        list: availableLOs,
        value: goDetailsProvider.getPathwaySelectedLO(loIndex),
        onChanged: (selectedLO) {
          if (availableLOs.isNotEmpty && selectedLO != 'No LOs available') {
            goDetailsProvider.setPathwaySelectedLO(loIndex, selectedLO);
          }
        },
      ),
    );
  }

  // Additional pathway creation helper methods
  Widget _buildRecursiveInputField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    return Container(
      height: 35,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(2),
      ),
      child: TextField(
        decoration: const InputDecoration(
          border: InputBorder.none,
          isDense: true,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          fillColor: Colors.transparent,
          hoverColor: Colors.transparent,
          contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 12),
          hintText: 'Input number',
          hintStyle: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        style: const TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: Colors.black,
          fontFamily: FontManager.fontFamilyTiemposText,
        ),
      ),
    );
  }

  // Dynamic dropdown methods for pathway entries
  Widget _buildDynamicLODropdown(
      int loIndex, int entryIndex, GoDetailsProvider goDetailsProvider) {
    final availableLOs =
        goDetailsProvider.getAvailableLOsForSequential(loIndex);
    final pathwayEntry = goDetailsProvider.getPathwayEntry(loIndex, entryIndex);

    // Get pathway type to determine validation rules
    final pathwayType = goDetailsProvider.getPathwaySelectedType(loIndex);

    // For alternative and parallel pathways, filter out LOs that are already selected
    List<String> filteredLOs = List.from(availableLOs);
    if ((pathwayType == 'Alternative' || pathwayType == 'Parallel') &&
        availableLOs.isNotEmpty) {
      // Get currently selected LO (the current LO index corresponds to an LO name)
      final currentLOName =
          goDetailsProvider.currentGoModel?.localObjectivesList?[loIndex].name;

      // Remove the current LO from available options to prevent self-selection
      if (currentLOName != null) {
        filteredLOs.removeWhere((lo) => lo == currentLOName);
      }

      // Remove already selected LOs in other pathway entries to prevent duplicates
      final allPathwayEntries = goDetailsProvider.getPathwayEntries(loIndex);
      for (int i = 0; i < allPathwayEntries.length; i++) {
        if (i != entryIndex) {
          // Don't remove the current entry's selection
          final selectedLOInOtherEntry = allPathwayEntries[i].selectedLO;
          if (selectedLOInOtherEntry != null &&
              selectedLOInOtherEntry.isNotEmpty) {
            filteredLOs.removeWhere((lo) => lo == selectedLOInOtherEntry);
          }
        }
      }
    }

    // Check if LOs are missing and show error message
    if (availableLOs.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 24,
            child: CustomDropdownWidget(
              label: 'Select LO',
              list: ['No LOs available'],
              value: null,
              onChanged: (value) {
                // Do nothing when no LOs are available
              },
            ),
          ),
          // Error message for missing LOs
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: SizedBox(
              width: double.infinity,
              child: Text(
                'LO is not available',
                textAlign: TextAlign.left,
                style: const TextStyle(
                  fontSize: 8,
                  color: Colors.red,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
          ),
        ],
      );
    }

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select LO',
        list: filteredLOs.isEmpty ? ['No LOs available'] : filteredLOs,
        value: pathwayEntry?.selectedLO,
        onChanged: (selectedLO) {
          if (filteredLOs.isNotEmpty && selectedLO != 'No LOs available') {
            // Additional validation for alternative and parallel pathways
            if (pathwayType == 'Alternative' || pathwayType == 'Parallel') {
              // Check if the selected LO is the current LO (should not happen due to filtering, but double-check)
              final currentLOName = goDetailsProvider
                  .currentGoModel?.localObjectivesList?[loIndex].name;
              if (selectedLO == currentLOName) {
                // Show error message or prevent selection
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        'Cannot select the same LO ($selectedLO) as the current LO'),
                    backgroundColor: Colors.red,
                    duration: Duration(seconds: 3),
                  ),
                );
                return;
              }

              // Check if the selected LO is already selected in another entry
              final allPathwayEntries =
                  goDetailsProvider.getPathwayEntries(loIndex);
              for (int i = 0; i < allPathwayEntries.length; i++) {
                if (i != entryIndex &&
                    allPathwayEntries[i].selectedLO == selectedLO) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                          'LO "$selectedLO" is already selected in another pathway entry'),
                      backgroundColor: Colors.red,
                      duration: Duration(seconds: 3),
                    ),
                  );
                  return;
                }
              }
            }

            goDetailsProvider.setPathwayEntrySelectedLO(
                loIndex, entryIndex, selectedLO);
          }
        },
      ),
    );
  }

  Widget _buildDynamicEntityAttributeDropdown({
    required int loIndex,
    required int entryIndex,
    required bool isAfterCondition,
    required GoDetailsProvider goDetailsProvider,
  }) {
    // Get entity attributes from ObjectCreationProvider

    List<String> entityAttributes =
        goDetailsProvider.getAttributeListBasedOnLo(loIndex, entryIndex);

    // Check if there's a current object with attributes
    // if (objectCreationProvider.currentObject?.attributes != null) {
    //   entityAttributes = objectCreationProvider.currentObject!.attributes!
    //       .where((attr) => attr.name != null && attr.name!.isNotEmpty)
    //       .map((attr) => attr.name!)
    //       .toList();
    // }
    //
    // // If no attributes from current object, try to get from objects list
    // if (entityAttributes.isEmpty && objectCreationProvider.objects.isNotEmpty) {
    //   // Get attributes from the first available object that has attributes
    //   for (final obj in objectCreationProvider.objects) {
    //     if (obj.attributes != null && obj.attributes!.isNotEmpty) {
    //       entityAttributes = obj.attributes!
    //           .where((attr) => attr.name != null && attr.name!.isNotEmpty)
    //           .map((attr) => attr.name!)
    //           .toList();
    //       break;
    //     }
    //   }
    // }

    final pathwayEntry = goDetailsProvider.getPathwayEntry(loIndex, entryIndex);

    String? currentValue = isAfterCondition
        ? pathwayEntry?.entityAttributeAfterCondition
        : pathwayEntry?.entityAttribute;

    // Check if attributes are missing and show error message
    if (entityAttributes.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 24,
            child: CustomDropdownWidget(
              label: 'Object Attribute',
              list: ['No attributes available'],
              value: null,
              onChanged: (value) {
                // Do nothing when no attributes are available
              },
            ),
          ),
          // Error message for missing attributes
          Padding(
            padding: const EdgeInsets.only(top: 2),
            child: Text(
              'Attributes are missing',
              style: const TextStyle(
                fontSize: 8,
                color: Colors.red,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
        ],
      );
    }

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Object Attribute',
        list: entityAttributes,
        value: currentValue,
        onChanged: (value) {
          if (value != null) {
            if (isAfterCondition) {
              goDetailsProvider.setPathwayEntryEntityAttributeAfterCondition(
                  loIndex, entryIndex, value);
            } else {
              goDetailsProvider.setPathwayEntryEntityAttribute(
                  loIndex, entryIndex, value);
            }
          }
        },
      ),
    );
  }

  Widget _buildDynamicConditionDropdown({
    required int loIndex,
    required int entryIndex,
    required GoDetailsProvider goDetailsProvider,
  }) {
    const List<String> conditions = [
      '==',
      '!=',
      '<',
      '<=',
      '>',
      '>=',
    ];

    final pathwayEntry = goDetailsProvider.getPathwayEntry(loIndex, entryIndex);

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Condition',
        list: conditions,
        value: pathwayEntry?.condition,
        onChanged: (value) {
          if (value != null) {
            goDetailsProvider.setPathwayEntryCondition(
                loIndex, entryIndex, value);
          }
        },
      ),
    );
  }

  Widget generatePathWayWidgetWithLineNumber(
      List<String> generatedPathway, int lineNumber) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        // Header for Pathway Definitions
        _buildLineWithNumber(
          lineNumber++,
          Text('PATHWAY DEFINITIONS:',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelMedium(context),
                fontWeight: FontWeight.w700,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
              )),
        ),
        const SizedBox(height: 8),

        // Pathway list with line numbers
        ...generatedPathway.asMap().entries.map((entry) {
          final index = entry.key;
          final pathway = entry.value;

          return _buildLineWithNumber(
            lineNumber + index,
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              // decoration: BoxDecoration(
              //   color: const Color(0xFFF7F7F7),
              //   borderRadius: BorderRadius.circular(4),
              //   border: Border.all(color: Colors.grey.shade300),
              // ),
              child: Text(pathway,
                  style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText)),
            ),
          );
        }).toList(),
      ],
    );
  }
}
