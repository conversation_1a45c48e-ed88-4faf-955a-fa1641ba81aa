import 'package:flutter/material.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/ai_generated_object_screen.dart';
import 'package:nsl/screens/web/new_design/nsl_java_content_parsing.dart';
import 'package:nsl/screens/web/new_design/nsl_java_content_parsing_screen.dart';
import 'package:nsl/screens/web/new_design/nsl_java_solutions.dart';
import 'package:nsl/screens/web/static_flow/ai_object_screen_static.dart';
import 'package:nsl/screens/web/static_flow/create_entity_screen.dart';
import 'package:nsl/screens/web/static_flow/create_publish_details_screen.dart';
import 'package:nsl/screens/web/static_flow/go_coming_soon_screen.dart';
import 'package:nsl/screens/web/new_design/discovery_chat_screen.dart';
import 'package:nsl/screens/web/new_design/manual_creation_screen.dart';
import 'package:nsl/screens/web/new_design/solution_modules_page.dart';
import 'package:nsl/screens/web/new_design/temp_multimedia/temp_web_chat.dart';
import 'package:nsl/screens/web/new_design/tree_hierarchy_model.dart';
import 'package:nsl/screens/web/new_design/web_add_modules_page.dart';
import 'package:nsl/screens/web/new_design/web_agent_screen.dart';
import 'package:nsl/screens/web/new_design/web_book_detail_page.dart';
import 'package:nsl/screens/web/new_design/web_book_solution_page.dart';
import 'package:nsl/screens/web/new_design/web_inside_book_module.dart';
import 'package:nsl/screens/web/new_design/widgets/create_widgets/web_create_solution_screen.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/web_home_screen_chat.dart';
import 'package:nsl/screens/web/new_design/web_my_library_screen.dart';
import 'package:nsl/screens/web/new_design/web_my_projects_screen.dart';
import 'package:nsl/screens/web/new_design/web_object_screen.dart';
import 'package:nsl/screens/web/new_design/web_solutions_screen.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/web_home_screen_chat_new.dart';
import 'package:nsl/screens/web/new_design/widgets/sidebar.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web_new/nsl_hierarchy_web_new/tree_hierarchy_model1_new.dart';
import 'package:nsl/screens/web/static_flow/create_object_screen_static.dart';
import 'package:nsl/screens/web/static_flow/manual_creation_static_Screen.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/tree_hierarchy_model1.dart';
import 'package:nsl/screens/web/static_flow/role_creation_screen.dart';
import 'package:nsl/screens/web/static_flow/web_home_screen_chat_static.dart';
// import 'package:nsl/screens/web/web_dashboard/dashboard_widgets.dart';
import 'package:nsl/screens/web_transaction/web_collection_module_widgets.dart';
import 'package:nsl/screens/web_transaction/web_collection_widgets.dart';
import 'package:nsl/screens/web_transaction/web_home_transaction.dart';
import 'package:nsl/screens/web_transaction/web_solution_widgets.dart';
import 'package:nsl/screens/web_transaction/web_transaction_collection.dart';
import 'package:nsl/screens/web_transaction/web_transaction_records.dart';
import 'package:nsl/screens/web_transaction/web_transaction_solution.dart';
import 'package:nsl/screens/web_transaction/web_transaction_widgets_demo.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/utils/logger.dart';

import 'package:provider/provider.dart';
import 'package:nsl/screens/web_transaction/web_my_transactions_widgets.dart';
import 'package:nsl/screens/web_creation_flow_mainscreen.dart';

import 'nsl_java_converter.dart';

class WebHomeScreenNew extends StatefulWidget {
  const WebHomeScreenNew({super.key});

  @override
  State<WebHomeScreenNew> createState() => _WebHomeScreenNewState();
}

// RoleInfo model is now imported from lib/models/role_info.dart

// EntityAttribute and EntityInfo are now replaced by Entity and Attribute from EntitiesData model

// Using EntityGroup directly from EntitiesData model

class _WebHomeScreenNewState extends State<WebHomeScreenNew> {
  @override
  Widget build(BuildContext context) {
    // Provider is now used directly in the Sidebar component
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      body: Row(
        children: [Sidebar(), Expanded(child: mainContent())],
      ),
    );
  }

  Widget mainContent() {
    final webHomeProvider = Provider.of<WebHomeProvider>(context);

    // Get the current screen index from the WebHomeProvider
    final currentScreenIndex = webHomeProvider.currentScreenIndex;

    // Log the current screen index
    Logger.info('Current screen index in mainContent: $currentScreenIndex');

    // If the current screen index is empty, set it to home
    if (currentScreenIndex.isEmpty) {
      // Use a post-frame callback to avoid setState during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          webHomeProvider.currentScreenIndex = ScreenConstants.home;
          Logger.info('Screen index was empty, set to home');
        }
      });
    }

    switch (currentScreenIndex) {
      case ScreenConstants.home:
        return WebHomeScreenChat();
      case ScreenConstants.nslRag:
        return WebHomeScreenChatNew();
      case ScreenConstants.create:
        //return WebMyLibraryScreen();
        return WebCreateSolutionScreen();
      case ScreenConstants.webCreationFlowMainScreen:
        return WebCreationFlowMainScreen();
      //  case ScreenConstants.myLibrary:
      //     return WebMyLibraryScreen();
      case ScreenConstants.nslJava:
        return NslJavaConverter();
      // return PreviewScreen();

      case ScreenConstants.webMyLibrary:
        return WebMyLibraryScreen();

      case ScreenConstants.webMyProjects:
        return WebMyProjectsScreen();

      case ScreenConstants.webBookDetailPage:
        return BookDetailPage();

      case ScreenConstants.webMySolution:
        return WebSolutionsScreen();

      case ScreenConstants.webInsideBookModule:
        return WebInsideBookModule();

      case ScreenConstants.webAddModulesPage:
        return WebAddModulesPage();

      case ScreenConstants.solutionModulesPage:
        return SolutionModulesPage();

      case ScreenConstants.webBookSolution:
        return WebBookSolutionPage();

      case ScreenConstants.webMyObject:
        return WebObjectScreen();
      case ScreenConstants.aiGeneratedObject:
        return AIGeneratedObjectScreen();
      case ScreenConstants.createObjectScreenStatic:
        return CreateObjectScreenStatic();
      case ScreenConstants.aiObjectScreenStatic:
        return AiObjectScreenStatic();
      case ScreenConstants.goComingSoonScreen:
        return GoComingSoonScreen();
      case ScreenConstants.createEntityScreen:
        return CreateEntityScreen();

      case ScreenConstants.manualGenerationSolution:
        return ManualCreationScreen();
      case ScreenConstants.myBusinessHome:
        return WebHomeTransaction();
      case ScreenConstants.myBusinessCollections:
        return WebTransactionCollection();
      case ScreenConstants.myBusinessSolutions:
        return WebTransactionSolution();
      case ScreenConstants.myBusinessRecords:
        return WebTransactionRecords();
      case ScreenConstants.webTransactionWidgetsDemo:
        return WebTransactionWidgetsDemo();
      case ScreenConstants.myBusinessCollectionsModule:
        return WebCollectionWidgets();
      case ScreenConstants.webSolutionWidgets:
        return WebSolutionWidgets();
      case ScreenConstants.tempWebChat:
        return TempWebChat();
      case ScreenConstants.webCollectionModuleDemo:
        return WebCollectionModuleDemo();
      case ScreenConstants.webAgentScreen:
        return WebAgentScreen();
      // case ScreenConstants.dashboardWidgets:
      //   return DashboardWidgets();

      case ScreenConstants.treeModel:
        return TreeHierarchyModel();
      case ScreenConstants.treeModel1:
        return TreeHierarchyModel1();

      case ScreenConstants.treeModelNew:
        return TreeHierarchyModel1New();

      case ScreenConstants.discovery:
        return DiscoveryChatScreen();

      case ScreenConstants.webHomeStatic:
        return WebHomeScreenChatStatic();

      case ScreenConstants.manualCreationStaticScreen:
        return ManualCreationStaticScreen();
      case ScreenConstants.nslJavaContentParsing:
        return NslJavaContentParsing();
      case ScreenConstants.nslJavaSolutionsScreen:
        return NslJavaSolutionsScreen();
      case ScreenConstants.nslJavaContentParsingScreen:
        return NslJavaContentParsingScreen();

      case ScreenConstants.createRole:
        return RoleCreationScreen();

      case ScreenConstants.createPublishDetailsScreen:
        return CreatePublishDetailsScreen();

      default:
        /* return Container(
          alignment: Alignment.center,
          child: Text('Coming Soon111',
              style: TextStyle(
                fontSize: 24,
                fontFamily: 'TiemposText',
                fontWeight: FontWeight.bold,
              )),
        ); */

        return WebMyTransactionsWidgets();
    }
  }
}
