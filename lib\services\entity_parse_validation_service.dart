import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:nsl/config/environment.dart';
import 'package:nsl/models/add_role_model.dart';
import 'package:nsl/models/parse_validation_entity/get_entity_attributes_model.dart';
import 'package:nsl/models/parse_validation_entity/get_entity_enum_values_model.dart';
import 'package:nsl/models/parse_validation_entity/get_entity_model.dart';
import 'package:nsl/models/parse_validation_entity/get_entity_relationship_model.dart';
import 'package:nsl/models/parse_validation_entity/parse_validation_entity_model.dart';
import 'package:nsl/models/roles/det_departments_list.dart';
import 'package:nsl/models/roles/get_roles_list.dart';

import 'package:nsl/models/roles/inheritance_role_model.dart';
import 'package:nsl/models/roles/publish_entity_success.dart';
import 'package:nsl/models/roles/save_valid_dept_model.dart';
import 'package:nsl/models/roles/validate_department_model.dart';
import 'package:nsl/models/save_role_model.dart';

import 'package:nsl/models/parse_validation_entity/publish_entity_model.dart';

import 'package:nsl/services/auth_service.dart';
import 'package:nsl/services/dio_client.dart';
import 'package:nsl/utils/logger.dart';

import '../models/solution/parse_validate_go.dart';

class EntityParseValidationService {
  final Dio _dio = DioClient().client;

  // Base URL
  late final String _baseUrl;

  late final String parseValidateEntities;

  late final String parseAddRoleEntities;
  late final String saveAddRoleEntities;
  late final String parseValidateDeptEntities;
  late final String saveAddDeptEntities;
  late final String parseValidateInheritanceEntities;
  late final String saveAddInheritanceEntities;

  late final String parseValidateSaveEntities;
  late final String parseValidateEntitiesAttributes;
  late final String parseValidateSaveEntitiesAttributes;
  late final String parseValidateEntitiesRelationShips;
  late final String parseValidateSaveEntitiesRelationShips;
  late final String parseValidateEntitiesAttributeValidations;
  late final String parseValidateSaveEntitiesAttributeValidations;
  late final String parseValidateEntitiesUiProperties;
  late final String parseValidateSaveEntitiesUiProperties;
  late final String parseValidateEntitiesSecurityProperties;
  late final String parseValidateSaveEntitiesSecurityProperties;
  late final String parseValidateEntitiesSystemPermissions;
  late final String parseValidateSaveEntitiesSystemPermissions;
  late final String parseValidateEntitiesEnumValues;
  late final String parseValidateSaveEntitiesEnumValues;
  late final String publishEntity;
  late final String publishRoleEntity;
  late final String publishDepartmentEntity;
  late final String roleDepartmentEntity;
//added by dp
  late final String validateGo;

  late final String saveMongoGo;
  late final String publishGoAPI;

  late final String getEntityUrl;
  late final String getEntityAttributesUrl;
  late final String getEntityRelationshipUrl;
  late final String getEntityEnumValuesUrl;

  // Singleton instance
  static final EntityParseValidationService _instance =
      EntityParseValidationService._internal();

  // Factory constructor
  factory EntityParseValidationService() => _instance;

  final authService = AuthService();

  // Internal constructor
  EntityParseValidationService._internal() {
    // Initialize base URL from environment
    _baseUrl = Environment.validateBaseUrl;
    parseValidateEntities =
        '$_baseUrl/api/entities/parse-and-validate/entities';

    parseAddRoleEntities = '$_baseUrl/api/roles/parse-and-validate/roles';
    saveAddRoleEntities = '$_baseUrl/api/roles/parse-validate-mongosave/roles';
    parseValidateDeptEntities =
        '$_baseUrl/api/roles/parse-and-validate/departments';
    saveAddDeptEntities =
        '$_baseUrl/api/roles/parse-validate-mongosave/departments';
    parseValidateInheritanceEntities =
        '$_baseUrl/api/roles/parse-and-validate/role-inheritance';
    saveAddInheritanceEntities =
        '$_baseUrl/api/roles/parse-validate-mongosave/role-inheritance';
    publishRoleEntity = '$_baseUrl/api/roles/deploy/single-role';
    publishDepartmentEntity =
        '$_baseUrl/api/roles/deploy/single-department/Engineering';
    roleDepartmentEntity = '$_baseUrl/library/table-data';

    // Create a new conversation

    parseValidateSaveEntities =
        '$_baseUrl/api/entities/parse-validate-mongosave/entities';

    parseValidateEntitiesAttributes =
        '$_baseUrl/api/entities/parse-and-validate/entity-attributes';
    parseValidateSaveEntitiesAttributes =
        '$_baseUrl/api/entities/parse-validate-mongosave/entity-attributes';
    parseValidateEntitiesRelationShips =
        '$_baseUrl/api/entities/parse-and-validate/entity-relationships';
    parseValidateSaveEntitiesRelationShips =
        '$_baseUrl/api/entities/parse-validate-mongosave/entity-relationships';
    parseValidateEntitiesAttributeValidations =
        '$_baseUrl/api/entities/parse-and-validate/attribute-validations';
    parseValidateSaveEntitiesAttributeValidations =
        '$_baseUrl/api/entities/parse-validate-mongosave/attribute-validations';
    parseValidateEntitiesUiProperties =
        '$_baseUrl/api/entities/parse-and-validate/ui-properties';
    parseValidateSaveEntitiesUiProperties =
        '$_baseUrl/api/entities/parse-validate-mongosave/ui-properties';
    parseValidateEntitiesSecurityProperties =
        '$_baseUrl/api/entities/parse-and-validate/security-properties';
    parseValidateSaveEntitiesSecurityProperties =
        '$_baseUrl/api/entities/parse-validate-mongosave/security-properties';
    parseValidateEntitiesSystemPermissions =
        '$_baseUrl/api/entities/parse-and-validate/role-system-permissions';
    parseValidateSaveEntitiesSystemPermissions =
        '$_baseUrl/api/entities/parse-validate-mongosave/system-permissions';
    parseValidateEntitiesEnumValues =
        '$_baseUrl/api/entities/parse-and-validate/attribute-enum-values';
    parseValidateSaveEntitiesEnumValues =
        '$_baseUrl/api/entities/parse-validate-mongosave/attribute-enum-values';
    publishEntity = '$_baseUrl/api/roles/deploy/entity-complete/';

    validateGo = '$_baseUrl/api/workflows-go/validate';
    saveMongoGo = '$_baseUrl/api/workflows-go/mongo-save';

    publishGoAPI = '$_baseUrl/api/production/deploy';

    getEntityUrl = '$_baseUrl/api/entities/fetch/entities/';
    getEntityAttributesUrl = '$_baseUrl/api/entities/fetch/entity-attributes/';
    getEntityRelationshipUrl =
        '$_baseUrl/api/entities/fetch/entity-relationships/';
    getEntityEnumValuesUrl =
        '$_baseUrl/api/entities/fetch/attribute-enum-values/';
  }

  Future<ParseValidationEntityModel?> parseValidateEntity(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateEntities,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidationEntityModel?> parseValidateSaveEntity(
      {String? nautralLanguage, bool isEditMode = false}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = isEditMode
          ? {
              'natural_language': nautralLanguage,
              'tenant_id': tenantId,
              "edit_draft": true
              // "edit_production": true
            }
          : {
              'natural_language': nautralLanguage,
              'tenant_id': tenantId,
            };

      // Make API call
      final response = await _dio.post(
        parseValidateSaveEntities,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidationEntityModel?> parseValidateEntityAttributes(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateEntitiesAttributes,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidationEntityModel?> parseValidateSaveEntityAttributes(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateSaveEntitiesAttributes,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidationEntityModel?> parseValidateEntityRelationships(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateEntitiesRelationShips,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidationEntityModel?> parseValidateSaveEntityRelationships(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateSaveEntitiesRelationShips,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidationEntityModel?> parseValidateEntityAttributeValidation(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateEntitiesAttributeValidations,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidationEntityModel?>
      parseValidateSaveEntityAttributeValidations(
          {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateSaveEntitiesAttributeValidations,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidationEntityModel?> parseValidateEntityEnumValues(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateEntitiesEnumValues,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidationEntityModel?> parseValidateSaveEntityEnumValues(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateSaveEntitiesEnumValues,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidationEntityModel?> parseValidateEntitySystemPermissions(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateEntitiesSystemPermissions,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidationEntityModel?> parseValidateSaveEntitySystemPermissions(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateSaveEntitiesSystemPermissions,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidationEntityModel?> parseValidateEntitySecurityProperties(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateEntitiesSecurityProperties,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidationEntityModel?> parseValidateSaveEntitySecurityProperties(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateSaveEntitiesSecurityProperties,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidationEntityModel parseValidationEntityModel =
            ParseValidationEntityModel.fromJson(response.data);
        return parseValidationEntityModel;
      } else {
        return ParseValidationEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<PublishEntityModel?> publish({String? entityId}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {
        "entity_id": entityId,
        'tenant_id': tenantId,
        "schema": "workflow_runtime",
        "created_by": "system",
      };

      // Make API call
      final response = await _dio.post(
        "$publishEntity$entityId",
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final PublishEntityModel publishEntityModel =
            PublishEntityModel.fromJson(response.data);
        return publishEntityModel;
      } else {
        return PublishEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<GetDepartmentsListModel?> fetchDepartments(
      {String? requiredTable}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {"tenant_id": tenantId, "required_table": requiredTable};
      log(payload.toString());

      // Make API call
      final response = await _dio.post(
        roleDepartmentEntity,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final GetDepartmentsListModel publishEntityModel =
            GetDepartmentsListModel.fromJson(response.data);
        return publishEntityModel;
      } else {
        return GetDepartmentsListModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<GetRolesListModel?> fetchRoles({String? requiredTable}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      final payload = {"tenant_id": tenantId, "required_table": requiredTable};
      log("======================================");
      // Make API call
      final response = await _dio.post(
        roleDepartmentEntity,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final GetRolesListModel publishEntityModel =
            GetRolesListModel.fromJson(response.data);
        return publishEntityModel;
      } else {
        return GetRolesListModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ValidateAddRoleModel?> parseValidateaddRoleEntity({
    String? nautralLanguage,
  }) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';

      // Prepare request payload
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Use different endpoint for edit mode
      final endpoint = parseAddRoleEntities;

      final response = await _dio.post(
        endpoint,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ValidateAddRoleModel parseValidationEntityModel =
            ValidateAddRoleModel.fromJson(response.data);
        return parseValidationEntityModel;
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
    return null;
  }

  Future<SaveRoleModel?> saveValidatedRoleEntity(
      {String? nautralLanguage, bool isEditMode = false}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      // Prepare request payload
      final payload = isEditMode
          ? {
              'natural_language': nautralLanguage,
              'tenant_id': tenantId,
              // 'edit_production': true,
              'edit_draft' : true
            }
          : {'natural_language': nautralLanguage, 'tenant_id': tenantId};

      // Make API call
      final response = await _dio.post(
        saveAddRoleEntities,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final SaveRoleModel parseValidationEntityModel =
            SaveRoleModel.fromJson(response.data);
        return parseValidationEntityModel;
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
    return null;
  }

  Future<ValidateDepartmentModel?> parseValidateDeptEntity(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      // Prepare request payload
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateDeptEntities,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ValidateDepartmentModel parseValidationEntityModel =
            ValidateDepartmentModel.fromJson(response.data);
        return parseValidationEntityModel;
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
    return null;
  }

  Future<SaveValidDepartmentModel?> saveValidatedDeptEntity(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      // Prepare request payload
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        saveAddDeptEntities,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final SaveValidDepartmentModel parseValidationEntityModel =
            SaveValidDepartmentModel.fromJson(response.data);
        return parseValidationEntityModel;
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
    return null;
  }

  Future<ValidateInheritanceModel?> parseValidateInheritanceEntity(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      // Prepare request payload
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        parseValidateInheritanceEntities,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ValidateInheritanceModel parseValidationEntityModel =
            ValidateInheritanceModel.fromJson(response.data);
        return parseValidationEntityModel;
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
    return null;
  }

  Future<ValidateAddRoleModel?> parseSaveInheritanceEntity(
      {String? nautralLanguage}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final tenantId = savedData.data?.user?.tenantId ?? '';
      // Prepare request payload
      final payload = {
        'natural_language': nautralLanguage,
        'tenant_id': tenantId
      };

      // Make API call
      final response = await _dio.post(
        saveAddInheritanceEntities,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ValidateAddRoleModel parseValidationEntityModel =
            ValidateAddRoleModel.fromJson(response.data);
        return parseValidationEntityModel;
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
    return null;
  }

  Future<PublishEntitySuccessModel?> publishRole(
      {String? entityId, isEditMode = false}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final name = savedData.data?.user?.name ?? '';
      var payload = {};
      if (isEditMode) {
        payload = {
          "schema": "workflow_runtime",
          "created_by": name,
        };
      } else {
        payload = {
          "schema": "workflow_runtime",
          "created_by": name,
          "edit_production": true
        };
      }

      // Make API call
      final response = await _dio.post(
        "$publishRoleEntity/$entityId",
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final PublishEntitySuccessModel publishEntityModel =
            PublishEntitySuccessModel.fromJson(response.data);
        return publishEntityModel;
      } else {
        return PublishEntitySuccessModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<PublishEntityModel?> publishDepartment({String? entityId}) async {
    try {
      final savedData = await authService.getSavedAuthData();
      final createdBy = savedData.data?.user?.tenantId ?? '';
      final payload = {
        "schema": "workflow_runtime",
        "created_by": createdBy,
      };

      // Make API call
      final response = await _dio.post(
        "$publishDepartmentEntity$entityId",
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final PublishEntityModel publishEntityModel =
            PublishEntityModel.fromJson(response.data);
        return publishEntityModel;
      } else {
        return PublishEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<GetEntityAttributesModel?> getEntityAttributes(
      {String? entityId}) async {
    try {
      final response = await _dio.get(
        "$getEntityAttributesUrl$entityId",
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final GetEntityAttributesModel getEntityAttributesModel =
            GetEntityAttributesModel.fromJson(response.data);
        return getEntityAttributesModel;
      } else {
        return GetEntityAttributesModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<GetEntityRelationshipModel?> getEntityRelationships(
      {String? entityId}) async {
    try {
      final response = await _dio.get(
        "$getEntityRelationshipUrl$entityId",
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final GetEntityRelationshipModel getEntityRelationshipModel =
            GetEntityRelationshipModel.fromJson(response.data);
        return getEntityRelationshipModel;
      } else {
        return GetEntityRelationshipModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<GetEntityEnumValueModel?> getEntityEnumValues(
      {String? entityId}) async {
    try {
      final response = await _dio.get(
        "$getEntityEnumValuesUrl$entityId",
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final GetEntityEnumValueModel getEntityEnumValueModel =
            GetEntityEnumValueModel.fromJson(response.data);
        return getEntityEnumValueModel;
      } else {
        return GetEntityEnumValueModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<GetEntityModel?> getEntity({String? entityId}) async {
    try {
      final response = await _dio.get(
        "$getEntityUrl$entityId",
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final GetEntityModel getEntityModel =
            GetEntityModel.fromJson(response.data);
        return getEntityModel;
      } else {
        return GetEntityModel.fromJson(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  Future<ParseValidateGo?> parseValidateGO({String? naturalLanguage}) async {
    try {
      // final savedData = await authService.getSavedAuthData();
      // final tenantId = savedData.data?.user?.tenantId ?? '';
      // HashMap<String,String> request = HashMap();
      // request['text'] = naturalLanguage!;
      FormData formData = FormData.fromMap({
        'text': naturalLanguage,
      });

      if (kDebugMode) {
        print(validateGo);
      }
      // Make API call
      final response = await _dio.post(
        validateGo,
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidateGo parseValidationEntityModel =
            JsonPojoConverter.fromJsonMap(response.data);
        return parseValidationEntityModel;
      } else {
        return JsonPojoConverter.fromJsonMap(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  parseValidateSaveGO(
      {required String naturalLanguage, required bool isEditMode}) async {
    try {
      // final savedData = await authService.getSavedAuthData();
      // final tenantId = savedData.data?.user?.tenantId ?? '';
      // HashMap<String,String> request = HashMap();
      // request['text'] = naturalLanguage!;
      FormData formData = FormData.fromMap({
        'text': naturalLanguage,
      });

      if (kDebugMode) {
        print(validateGo);
      }
      // Make API call
      final response = await _dio.post(
        saveMongoGo,
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidateGo parseValidationEntityModel =
            JsonPojoConverter.fromJsonMap(response.data);
        return parseValidationEntityModel;
      } else {
        return JsonPojoConverter.fromJsonMap(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }

  publishGo({required String naturalLanguage}) async {
    try {
      FormData formData = FormData.fromMap({
        'text': naturalLanguage,
      });

      if (kDebugMode) {
        print(validateGo);
      }
      // Make API call
      final response = await _dio.post(
        publishGoAPI,
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final ParseValidateGo parseValidationEntityModel =
            JsonPojoConverter.fromJsonMap(response.data);
        return parseValidationEntityModel;
      } else {
        return JsonPojoConverter.fromJsonMap(response.data);
      }
    } catch (e, s) {
      Logger.error('Error creating conversation: $e $s');
      throw Exception(e);
    }
  }
}
