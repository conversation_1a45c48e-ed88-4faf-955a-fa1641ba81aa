import 'package:dio/dio.dart';
import 'package:nsl/screens/web/static_flow/nsl_entity_parser.dart';
import '../models/object_creation_model.dart';
import 'base_api_service.dart';
import 'auth_service.dart';
import '../utils/logger.dart';
import '../config/environment.dart';

/// Service for handling object creation/extraction API operations with session-based API
class ObjectCreationService extends BaseApiService {
  // Base URL for the new extraction API (configurable for different environments)
  String get _baseUrl => Environment.objectCreationApiBaseUrl;
  static const String _apiVersion = '/api/v1';

  // Alternative base URL for network deployment (uncomment if needed)
  // static const String _baseUrl = 'http://10.26.1.11:8630';

  // Auth service for getting user data
  final AuthService _authService = AuthService();

  // Configuration constants
  static const int _maxRetries = 3;
  static const int _retryDelayMs = 1000; // Fixed 1 second delay

  /// Execute an operation with retry logic
  Future<T> _executeWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
    int maxRetries = _maxRetries,
  }) async {
    int attempt = 0;

    while (attempt <= maxRetries) {
      try {
        if (attempt > 0) {
          Logger.info(
              '${operationName ?? 'Operation'} retry attempt $attempt/$maxRetries');
        }
        return await operation();
      } catch (e) {
        attempt++;

        // Check if we should retry this error
        bool shouldRetry = _shouldRetryError(e);

        if (attempt > maxRetries || !shouldRetry) {
          Logger.error(
              '${operationName ?? 'Operation'} failed after $attempt attempts: $e');
          rethrow;
        }

        // Fixed delay instead of exponential backoff
        Logger.warning(
            '${operationName ?? 'Operation'} failed (attempt $attempt), retrying in ${_retryDelayMs}ms: $e');
        await Future.delayed(Duration(milliseconds: _retryDelayMs));
      }
    }

    throw Exception(
        '${operationName ?? 'Operation'} failed after $maxRetries retries');
  }

  /// Determine if an error should trigger a retry
  bool _shouldRetryError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
        case DioExceptionType.connectionError:
          return true;
        case DioExceptionType.badResponse:
          // Retry on server errors (5xx) but not client errors (4xx)
          final statusCode = error.response?.statusCode;
          return statusCode != null && statusCode >= 500;
        default:
          return false;
      }
    }
    return false;
  }

  /// Get user-friendly error message
  String getUserFriendlyMessage(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'Request timed out. Please check your connection and try again.';
        case DioExceptionType.connectionError:
          return 'Unable to connect to the server. Please check your internet connection.';
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          if (statusCode == 401) {
            return 'Authentication failed. Please log in again.';
          } else if (statusCode == 403) {
            return 'You do not have permission to perform this action.';
          } else if (statusCode == 404) {
            return 'The requested resource was not found.';
          } else if (statusCode != null && statusCode >= 500) {
            return 'Server error occurred. Please try again later.';
          }
          return error.response?.data?['message'] ?? 'Request failed';
        default:
          return 'An unexpected error occurred. Please try again.';
      }
    }
    return error.toString();
  }

  /// Make API call using Dio
  Future<Response> _makeApiCall({
    required String method,
    required String url,
    Map<String, dynamic>? data,
    Options? options,
  }) async {
    final dio = Dio();

    switch (method.toUpperCase()) {
      case 'GET':
        return await dio.get(url, options: options);
      case 'POST':
        return await dio.post(url, data: data, options: options);
      case 'PUT':
        return await dio.put(url, data: data, options: options);
      case 'DELETE':
        return await dio.delete(url, options: options);
      default:
        throw Exception('Unsupported HTTP method: $method');
    }
  }

  /// Create a new session for entity extraction
  Future<ObjectCreationResponse> createSession({
    required String originalIntent,
  }) async {
    return await _executeWithRetry<ObjectCreationResponse>(
      () async {
        Logger.info('Creating new session with intent: $originalIntent');

        final endpoint = '$_apiVersion/sessions';
        final fullUrl = '$_baseUrl$endpoint';
        final token = await _authService.getValidToken();

        final options = Options(
          headers: {
            'Content-Type': 'application/json',
            if (token != null) 'Authorization': 'Bearer $token',
          },
        );

        final requestData = {
          'original_intent': originalIntent,
        };

        // final response = await _makeApiCall(
        //   method: 'POST',
        //   url: fullUrl,
        //   data: requestData,
        //   options: options,
        // );
        final response = Response(
          data: {
            "success": true,
            "session_id": "56c3f775-5b8e-4214-9af3-0a267012e4fe",
            "message": "Session created successfully"
          },
          statusCode: 200,
          statusMessage: 'OK',
          headers: Headers(),
          requestOptions: RequestOptions(path: fullUrl),
          isRedirect: false,
          redirects: [],
          extra: null,
        );

        Logger.info('Session creation response: ${response.statusCode}');
        Logger.info('Session creation response data: ${response.data}');

        if (response.statusCode == 200 || response.statusCode == 201) {
          final sessionId = response.data?['session_id'] ??
              response.data?['id'] ??
              response.data?['sessionId'];

          if (sessionId != null) {
            Logger.info('Session created successfully with ID: $sessionId');
            return ObjectCreationResponse(
              success: true,
              message: 'Session created successfully',
              sessionId: sessionId.toString(),
            );
          } else {
            Logger.error('Session created but no session ID found in response');
            Logger.error('Response data keys: ${response.data?.keys.toList()}');
            throw Exception('Session created but no session ID returned');
          }
        } else {
          Logger.error(
              'Session creation failed: ${response.statusCode} - ${response.statusMessage}');
          Logger.error('Error response data: ${response.data}');
          throw Exception(
              'HTTP ${response.statusCode}: ${response.statusMessage}');
        }
      },
      operationName: 'createSession',
    );
  }

  /// Execute comprehensive extraction for a session
  Future<ObjectCreationResponse> executeComprehensiveExtraction({
    required String sessionId,
    required String intent,
    required String tenantName,
    required String businessDomain,
    required String context,
  }) async {
    return await _executeWithRetry<ObjectCreationResponse>(
      () async {
        Logger.info(
            'Executing comprehensive extraction for session: $sessionId');

        // Use the correct API endpoint format: /api/v1/extract/comprehensive/{sessionId}
        final endpoint = '$_apiVersion/extract/comprehensive/$sessionId';
        final fullUrl = '$_baseUrl$endpoint';
        final token = await _authService.getValidToken();

        final options = Options(
          headers: {
            'Content-Type': 'application/json',
            if (token != null) 'Authorization': 'Bearer $token',
          },
        );

        // Use the exact request body format as specified in the API documentation
        final requestData = {
          'intent': intent,
          'tenant_name': tenantName,
          'business_domain': businessDomain,
          'context': context,
        };

        Logger.info('Making comprehensive extraction request to: $fullUrl');
        Logger.info('Request data: $requestData');

        // final response = await _makeApiCall(
        //   method: 'POST',
        //   url: fullUrl,
        //   data: requestData,
        //   options: options,
        // );

        final response = Response(
          data: {
            "success": true,
            "message": "Comprehensive extraction completed successfully",
            "session_id": "56c3f775-5b8e-4214-9af3-0a267012e4fe",
            "data": {
              "total_entities": 12,
              "total_roles": 4,
              "total_go": 2,
              "total_lo": 4,
              "total_attributes": 161,
              "total_business_rules": 0,
              "total_permissions": 8,
              "has_extractions": true,
              "last_updated": "2025-07-22T09:22:13.551821"
            }
          },
          statusCode: 200,
          statusMessage: 'OK',
          headers: Headers(),
          requestOptions: RequestOptions(
              path: '$_baseUrl$_apiVersion/extract/comprehensive/$sessionId'),
          isRedirect: false,
          redirects: [],
          extra: null,
        );

        Logger.info(
            'Comprehensive extraction response: ${response.statusCode}');
        Logger.info('Response data: ${response.data}');

        if (response.statusCode == 200 || response.statusCode == 201) {
          // Parse the response according to API documentation
          if (response.data != null && response.data is Map<String, dynamic>) {
            final responseMap = response.data as Map<String, dynamic>;
            final success = responseMap['success'] ?? false;
            final message = responseMap['message']?.toString() ??
                'Comprehensive extraction completed';
            final returnedSessionId =
                responseMap['session_id']?.toString() ?? sessionId;

            if (success) {
              // Parse the data section with statistics
              final data = responseMap['data'];
              if (data != null && data is Map<String, dynamic>) {
                final totalEntities = data['total_entities']?.toInt() ?? 0;
                final totalRoles = data['total_roles']?.toInt() ?? 0;
                final totalGo = data['total_go']?.toInt() ?? 0;
                final totalLo = data['total_lo']?.toInt() ?? 0;
                final hasExtractions = data['has_extractions'] ?? false;
                final lastUpdated = data['last_updated']?.toString();

                Logger.info('Comprehensive extraction statistics:');
                Logger.info('  - Total entities: $totalEntities');
                Logger.info('  - Total roles: $totalRoles');
                Logger.info('  - Total GO: $totalGo');
                Logger.info('  - Total LO: $totalLo');
                Logger.info('  - Has extractions: $hasExtractions');
                Logger.info('  - Last updated: $lastUpdated');
              }

              Logger.info('Comprehensive extraction completed successfully');
              return ObjectCreationResponse(
                success: true,
                message: message,
                sessionId: returnedSessionId,
              );
            } else {
              throw Exception(message);
            }
          } else {
            // Unexpected response format - throw error instead of fallback
            Logger.error(
                'Unexpected response format from comprehensive extraction API');
            Logger.error('Response data: ${response.data}');
            throw Exception('API returned unexpected response format');
          }
        } else {
          Logger.error(
              'Comprehensive extraction failed: ${response.statusCode} - ${response.statusMessage}');
          Logger.error('Error response data: ${response.data}');
          throw Exception(
              'HTTP ${response.statusCode}: ${response.statusMessage}');
        }
      },
      operationName: 'executeComprehensiveExtraction',
    );
  }

  /// Get entities for a specific session
  Future<ObjectCreationResponse> getSessionEntities(String sessionId) async {
    return await _executeWithRetry<ObjectCreationResponse>(
      () async {
        Logger.info('Fetching entities for session: $sessionId');

        final endpoint = '$_apiVersion/sessions/$sessionId/entities';
        final fullUrl = '$_baseUrl$endpoint';
        final token = await _authService.getValidToken();

        final options = Options(
          headers: {
            'Content-Type': 'application/json',
            if (token != null) 'Authorization': 'Bearer $token',
          },
        );

        // final response = await _makeApiCall(
        //   method: 'GET',
        //   url: fullUrl,
        //   options: options,
        // );

        final response = Response(
          data: {
            "success": true,
            "session_id": "56c3f775-5b8e-4214-9af3-0a267012e4fe",
            "entityMetadata": [
              {
                "tenant": "t001",
                "entityDeclaration":
                    "Customer1 has customerId^PK, firstName* [required], lastName* [required], email* [unique] [required], phone [information], dateOfBirth [information], ssn^FK [unique] [required], address [information], city [information], state* (Active, Inactive, Suspended, Closed) [default: Active], zipCode [information], accountOpenDate [derived], customerStatus* (Active, Inactive, Suspended, Closed) [default: Active], creditLimit [dependent], totalDebt [dependent]",
                "name": "Customer1",
                "displayName": "Customer1",
                "type": "master",
                "description":
                    "Represents a bank customer who can apply for loans",
                "businessDomain": "CRM",
                "category": "Customer Data",
                "tags": ["customer", "profile", "personal", "banking"],
                "archivalStrategy": "archive_only",
                "colorTheme": "green",
                "icon": "user",
                "attributes": [
                  {
                    "attributeName": "customerId",
                    "displayName": "Customer ID",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description": "Unique identifier for the customer",
                    "helperText": "Auto-generated customer identifier"
                  },
                  {
                    "attributeName": "firstName",
                    "displayName": "First Name",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Customer's first name",
                    "helperText": "Enter customer's first name"
                  },
                  {
                    "attributeName": "lastName",
                    "displayName": "Last Name",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Customer's last name",
                    "helperText": "Enter customer's last name"
                  },
                  {
                    "attributeName": "email",
                    "displayName": "Email Address",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Customer's email address",
                    "helperText": "Enter valid email address"
                  },
                  {
                    "attributeName": "phone",
                    "displayName": "Phone Number",
                    "dataType": "string",
                    "required": false,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Customer's phone number",
                    "helperText": "Enter customer's phone number"
                  },
                  {
                    "attributeName": "dateOfBirth",
                    "displayName": "Date of Birth",
                    "dataType": "date",
                    "required": false,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Customer's date of birth",
                    "helperText": "Enter customer's date of birth"
                  },
                  {
                    "attributeName": "ssn",
                    "displayName": "Social Security Number",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Customer's social security number",
                    "helperText": "Enter customer's SSN"
                  },
                  {
                    "attributeName": "address",
                    "displayName": "Address",
                    "dataType": "string",
                    "required": false,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Customer's address",
                    "helperText": "Enter customer's address"
                  },
                  {
                    "attributeName": "city",
                    "displayName": "City",
                    "dataType": "string",
                    "required": false,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Customer's city",
                    "helperText": "Enter customer's city"
                  },
                  {
                    "attributeName": "state",
                    "displayName": "State",
                    "dataType": "string",
                    "required": false,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Customer's state",
                    "helperText": "Enter customer's state"
                  },
                  {
                    "attributeName": "zipCode",
                    "displayName": "Zip Code",
                    "dataType": "string",
                    "required": false,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Customer's zip code",
                    "helperText": "Enter customer's zip code"
                  },
                  {
                    "attributeName": "accountOpenDate",
                    "displayName": "Account Open Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Date when customer account was opened",
                    "helperText": "Auto-generated account open date"
                  },
                  {
                    "attributeName": "customerStatus",
                    "displayName": "Customer Status",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "Active",
                    "description": "Current status of the customer account",
                    "helperText": "Select customer status"
                  },
                  {
                    "attributeName": "creditLimit",
                    "displayName": "Credit Limit",
                    "dataType": "decimal",
                    "required": false,
                    "unique": false,
                    "defaultType": "calculated",
                    "defaultValue": "0.00",
                    "description": "Customer's credit limit",
                    "helperText": "Calculated credit limit"
                  },
                  {
                    "attributeName": "totalDebt",
                    "displayName": "Total Debt",
                    "dataType": "decimal",
                    "required": false,
                    "unique": false,
                    "defaultType": "calculated",
                    "defaultValue": "0.00",
                    "description": "Customer's total debt",
                    "helperText": "Calculated total debt"
                  }
                ],
                "entityRelationships": [
                  {
                    "primaryEntity": "Customer",
                    "relatedEntity": "LoanApplication",
                    "primaryKey": "Customer.customerId",
                    "foreignKey": "LoanApplication.customerId",
                    "relationshipType": "one-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description":
                        "Each customer can have multiple loan applications"
                  },
                  {
                    "primaryEntity": "Customer",
                    "relatedEntity": "Account",
                    "primaryKey": "Customer.customerId",
                    "foreignKey": "Account.customerId",
                    "relationshipType": "one-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Each customer can have multiple accounts"
                  }
                ],
                "attributeBusinessRules": [
                  {
                    "entityName": "Customer",
                    "attributeName": "email",
                    "leftOperand": "email",
                    "operator": "IS_NOT_NULL",
                    "rightOperand": "",
                    "successValueRange": "",
                    "warningValueRange": "",
                    "failureValueRange": "",
                    "multiConditionOperator": "FALSE",
                    "warningMessage": "",
                    "successMessage": "Email validation passed",
                    "errorMessage": "Email is required"
                  },
                  {
                    "entityName": "Customer",
                    "attributeName": "firstName",
                    "leftOperand": "firstName",
                    "operator": "LENGTH_GREATER_THAN",
                    "rightOperand": "0",
                    "successValueRange": "",
                    "warningValueRange": "",
                    "failureValueRange": "",
                    "multiConditionOperator": "FALSE",
                    "warningMessage": "",
                    "successMessage": "First name validation passed",
                    "errorMessage": "First name is required"
                  },
                  {
                    "entityName": "Customer",
                    "attributeName": "lastName",
                    "leftOperand": "lastName",
                    "operator": "LENGTH_GREATER_THAN",
                    "rightOperand": "0",
                    "successValueRange": "",
                    "warningValueRange": "",
                    "failureValueRange": "",
                    "multiConditionOperator": "FALSE",
                    "warningMessage": "",
                    "successMessage": "Last name validation passed",
                    "errorMessage": "Last name is required"
                  },
                  {
                    "entityName": "Customer",
                    "attributeName": "ssn",
                    "leftOperand": "ssn",
                    "operator": "IS_NOT_NULL",
                    "rightOperand": "",
                    "successValueRange": "",
                    "warningValueRange": "",
                    "failureValueRange": "",
                    "multiConditionOperator": "FALSE",
                    "warningMessage": "",
                    "successMessage": "SSN validation passed",
                    "errorMessage": "SSN is required"
                  },
                  {
                    "entityName": "Customer",
                    "attributeName": "creditLimit",
                    "leftOperand": "creditLimit",
                    "operator": "GREATER_THAN_OR_EQUAL",
                    "rightOperand": "0",
                    "successValueRange": "",
                    "warningValueRange": "",
                    "failureValueRange": "",
                    "multiConditionOperator": "FALSE",
                    "warningMessage": "",
                    "successMessage": "Credit limit validation passed",
                    "errorMessage": "Credit limit must be non-negative"
                  }
                ],
                "uiProperties": [
                  {
                    "Entity.Attribute": "Customer.customerId",
                    "controlType": "text_input",
                    "displayFormat": "text",
                    "inputMask": "",
                    "placeholderText": "Auto-generated",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Customer ID",
                    "requiredIndicator": true
                  },
                  {
                    "Entity.Attribute": "Customer.firstName",
                    "controlType": "text_input",
                    "displayFormat": "text",
                    "inputMask": "",
                    "placeholderText": "Enter first name",
                    "autoComplete": true,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "First Name",
                    "requiredIndicator": true
                  },
                  {
                    "Entity.Attribute": "Customer.lastName",
                    "controlType": "text_input",
                    "displayFormat": "text",
                    "inputMask": "",
                    "placeholderText": "Enter last name",
                    "autoComplete": true,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Last Name",
                    "requiredIndicator": true
                  },
                  {
                    "Entity.Attribute": "Customer.email",
                    "controlType": "email_input",
                    "displayFormat": "email",
                    "inputMask": "",
                    "placeholderText": "Enter email address",
                    "autoComplete": true,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Email Address",
                    "requiredIndicator": true
                  },
                  {
                    "Entity.Attribute": "Customer.phone",
                    "controlType": "phone_input",
                    "displayFormat": "phone",
                    "inputMask": "(###) ###-####",
                    "placeholderText": "Enter phone number",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Phone Number",
                    "requiredIndicator": false
                  },
                  {
                    "Entity.Attribute": "Customer.dateOfBirth",
                    "controlType": "date_picker",
                    "displayFormat": "date",
                    "inputMask": "",
                    "placeholderText": "Select date of birth",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Date of Birth",
                    "requiredIndicator": false
                  },
                  {
                    "Entity.Attribute": "Customer.ssn",
                    "controlType": "text_input",
                    "displayFormat": "text",
                    "inputMask": "###-##-####",
                    "placeholderText": "Enter SSN",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Social Security Number",
                    "requiredIndicator": true
                  },
                  {
                    "Entity.Attribute": "Customer.address",
                    "controlType": "text_area",
                    "displayFormat": "text",
                    "inputMask": "",
                    "placeholderText": "Enter address",
                    "autoComplete": true,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Address",
                    "requiredIndicator": false
                  },
                  {
                    "Entity.Attribute": "Customer.city",
                    "controlType": "text_input",
                    "displayFormat": "text",
                    "inputMask": "",
                    "placeholderText": "Enter city",
                    "autoComplete": true,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "City",
                    "requiredIndicator": false
                  },
                  {
                    "Entity.Attribute": "Customer.state",
                    "controlType": "dropdown",
                    "displayFormat": "text",
                    "inputMask": "",
                    "placeholderText": "Select state",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "State",
                    "requiredIndicator": false
                  },
                  {
                    "Entity.Attribute": "Customer.zipCode",
                    "controlType": "text_input",
                    "displayFormat": "text",
                    "inputMask": "#####",
                    "placeholderText": "Enter zip code",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Zip Code",
                    "requiredIndicator": false
                  },
                  {
                    "Entity.Attribute": "Customer.accountOpenDate",
                    "controlType": "datetime_picker",
                    "displayFormat": "datetime",
                    "inputMask": "",
                    "placeholderText": "Auto-generated",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Account Open Date",
                    "requiredIndicator": true
                  },
                  {
                    "Entity.Attribute": "Customer.customerStatus",
                    "controlType": "dropdown",
                    "displayFormat": "text",
                    "inputMask": "",
                    "placeholderText": "Select status",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Customer Status",
                    "requiredIndicator": true
                  },
                  {
                    "Entity.Attribute": "Customer.creditLimit",
                    "controlType": "currency_input",
                    "displayFormat": "currency",
                    "inputMask": "",
                    "placeholderText": "Calculated",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Credit Limit",
                    "requiredIndicator": false
                  },
                  {
                    "Entity.Attribute": "Customer.totalDebt",
                    "controlType": "currency_input",
                    "displayFormat": "currency",
                    "inputMask": "",
                    "placeholderText": "Calculated",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Total Debt",
                    "requiredIndicator": false
                  }
                ],
                "securityClassification": [
                  {
                    "Entity.Attribute": "Customer.customerId",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": []
                  },
                  {
                    "Entity.Attribute": "Customer.firstName",
                    "classification": "confidential",
                    "piiType": "personal",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "X***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["GDPR", "CCPA"]
                  },
                  {
                    "Entity.Attribute": "Customer.lastName",
                    "classification": "confidential",
                    "piiType": "personal",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "X***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["GDPR", "CCPA"]
                  },
                  {
                    "Entity.Attribute": "Customer.email",
                    "classification": "confidential",
                    "piiType": "contact",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***@***.com",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["GDPR", "CCPA"]
                  },
                  {
                    "Entity.Attribute": "Customer.phone",
                    "classification": "confidential",
                    "piiType": "contact",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "(***) ***-####",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["GDPR", "CCPA"]
                  },
                  {
                    "Entity.Attribute": "Customer.dateOfBirth",
                    "classification": "confidential",
                    "piiType": "personal",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "**/**/****",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["GDPR", "CCPA"]
                  },
                  {
                    "Entity.Attribute": "Customer.ssn",
                    "classification": "restricted",
                    "piiType": "personal",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***-**-####",
                    "accessLevel": "admin_only",
                    "auditTrail": true,
                    "dataResidency": "us_only",
                    "retentionOverride": "7_years",
                    "anonymizationRequired": true,
                    "anonymizationMethod": "encryption",
                    "complianceFrameworks": ["GDPR", "CCPA", "SOX", "PCI-DSS"]
                  },
                  {
                    "Entity.Attribute": "Customer.address",
                    "classification": "confidential",
                    "piiType": "personal",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "*** ***** ***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["GDPR", "CCPA"]
                  },
                  {
                    "Entity.Attribute": "Customer.city",
                    "classification": "internal",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": []
                  },
                  {
                    "Entity.Attribute": "Customer.state",
                    "classification": "internal",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": []
                  },
                  {
                    "Entity.Attribute": "Customer.zipCode",
                    "classification": "internal",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": []
                  },
                  {
                    "Entity.Attribute": "Customer.accountOpenDate",
                    "classification": "internal",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": []
                  },
                  {
                    "Entity.Attribute": "Customer.customerStatus",
                    "classification": "internal",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": []
                  },
                  {
                    "Entity.Attribute": "Customer.creditLimit",
                    "classification": "confidential",
                    "piiType": "financial",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***,***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["SOX", "PCI-DSS"]
                  },
                  {
                    "Entity.Attribute": "Customer.totalDebt",
                    "classification": "confidential",
                    "piiType": "financial",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***,***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["SOX", "PCI-DSS"]
                  }
                ],
                "enumeratedValues": [
                  {
                    "Entity.Attribute": "Customer.customerStatus",
                    "enumName": "CustomerStatus",
                    "value": "Active",
                    "display": "Active",
                    "description": "Customer account is active",
                    "sortOrder": 1,
                    "active": true
                  },
                  {
                    "Entity.Attribute": "Customer.customerStatus",
                    "enumName": "CustomerStatus",
                    "value": "Inactive",
                    "display": "Inactive",
                    "description": "Customer account is inactive",
                    "sortOrder": 2,
                    "active": true
                  },
                  {
                    "Entity.Attribute": "Customer.customerStatus",
                    "enumName": "CustomerStatus",
                    "value": "Suspended",
                    "display": "Suspended",
                    "description": "Customer account is suspended",
                    "sortOrder": 3,
                    "active": true
                  },
                  {
                    "Entity.Attribute": "Customer.customerStatus",
                    "enumName": "CustomerStatus",
                    "value": "Closed",
                    "display": "Closed",
                    "description": "Customer account is closed",
                    "sortOrder": 4,
                    "active": true
                  }
                ],
                "systemPermissions": [
                  {
                    "permissionId": "PERM_ENTITY_CUSTOMER",
                    "permissionName": "Customer Entity Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "customer",
                    "actions": ["create", "read", "update", "delete"],
                    "description": "Full access to customer entity",
                    "scope": "tenant_records",
                    "naturalLanguage": "Permission to manage customer records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "permissionId": "PERM_CUSTOMER_READ_ONLY",
                    "permissionName": "Customer Read Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "customer",
                    "actions": ["read"],
                    "description": "Read-only access to customer data",
                    "scope": "own_records",
                    "naturalLanguage": "Permission to read customer records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "permissionId": "PERM_CUSTOMER_PII_ACCESS",
                    "permissionName": "Customer PII Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "customer",
                    "actions": ["read"],
                    "description": "Access to customer PII data",
                    "scope": "admin_only",
                    "naturalLanguage": "Permission to access customer PII data",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "roleSystemPermissions": [
                  {
                    "roleId": "ROLE_ADMIN",
                    "permissionId": "PERM_ENTITY_CUSTOMER",
                    "grantedActions": ["create", "read", "update", "delete"],
                    "rowLevelConditions": {"access_level": "tenant_records"},
                    "naturalLanguage":
                        "Admin has full access to customer records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_MANAGER",
                    "permissionId": "PERM_ENTITY_CUSTOMER",
                    "grantedActions": ["create", "read", "update"],
                    "rowLevelConditions": {
                      "access_level": "department_records"
                    },
                    "naturalLanguage":
                        "Manager can manage customer records in their department",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_USER",
                    "permissionId": "PERM_CUSTOMER_READ_ONLY",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "own_records_only"},
                    "naturalLanguage":
                        "User can read their own customer records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_HR_ADMIN",
                    "permissionId": "PERM_CUSTOMER_PII_ACCESS",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "hr_records"},
                    "naturalLanguage": "HR Admin can access customer PII data",
                    "version": 1,
                    "status": "active"
                  }
                ]
              }
            ]
          },
          statusCode: 200,
          statusMessage: 'OK',
          headers: Headers(),
          requestOptions: RequestOptions(path: fullUrl),
          isRedirect: false,
          redirects: [],
          extra: null,
        );

        Logger.info('Session entities response: ${response.statusCode}');
        Logger.info('Response data type: ${response.data.runtimeType}');
        Logger.info('Response data: ${response.data}');

        if (response.statusCode == 200) {
          // Parse the response data - handle both direct array and object with entities array
          List<dynamic> entitiesData;

          if (response.data is List) {
            // Direct array format
            Logger.info('Processing direct array format');
            entitiesData = response.data as List;
          } else if (response.data is Map<String, dynamic>) {
            // Object format with entities array (new API format)
            Logger.info('Processing object format with entities array');
            final responseMap = response.data as Map<String, dynamic>;
            Logger.info('Response map keys: ${responseMap.keys.toList()}');

            if (responseMap.containsKey('entityMetadata') &&
                responseMap['entityMetadata'] is List) {
              entitiesData = responseMap['entityMetadata'] as List;
              Logger.info(
                  'Found entities array with ${entitiesData.length} items');
            } else {
              Logger.error(
                  'Response map does not contain valid entities array');
              Logger.error('Available keys: ${responseMap.keys.toList()}');
              throw Exception(
                  'Invalid response format: expected entities array in response object');
            }
          } else {
            Logger.error(
                'Unexpected response data type: ${response.data.runtimeType}');
            throw Exception(
                'Invalid response format: expected list of entities or object with entities array');
          }

          try {
            final entities = entitiesData.map((item) {
              try {
                return ObjectCreationModel.fromJson(item);
              } catch (e) {
                Logger.warning('Failed to parse entity item: $e');
                Logger.warning('Entity item data: $item');

                // Return a minimal entity to avoid breaking the entire response
                return ObjectCreationModel(
                  id: item['id']?.toString() ?? 'unknown',
                  name: item['name']?.toString() ?? 'Unknown Entity',
                  displayName: item['displayName']?.toString() ??
                      item['name']?.toString() ??
                      'Unknown Entity',
                  description: 'Failed to parse entity data',
                );
              }
            }).toList();

            Logger.info(
                'Successfully fetched ${entities.length} entities for session: $sessionId');
            final resolut = ObjectCreationResponse(
              success: true,
              message: 'Successfully fetched entities',
              data: entities,
              sessionId: sessionId,
            );

            print(
                "Entity Parser : ${EntityTextFormatter.toNarrative(resolut.data!.first)}");
            return resolut;
          } catch (e) {
            Logger.error('Failed to parse entities data: $e');
            throw Exception('Failed to parse entities: $e');
          }
        } else {
          throw Exception(
              'HTTP ${response.statusCode}: ${response.statusMessage}');
        }
      },
      operationName: 'getSessionEntities',
    );
  }

  /// Execute complete extraction workflow with new API endpoints
  Future<ObjectCreationResponse> executeCompleteExtractionWorkflowNew({
    required String userIntent,
    String? tenantName,
    String? businessDomain,
    String? context,
  }) async {
    return await _executeWithRetry<ObjectCreationResponse>(
      () async {
        Logger.info('Starting complete extraction workflow (new API)');

        // Step 1: Create session
        final sessionResponse = await createSession(
          originalIntent: userIntent,
        );

        if (!sessionResponse.success || sessionResponse.sessionId == null) {
          throw Exception(
              sessionResponse.message ?? 'Failed to create session');
        }

        final sessionId = sessionResponse.sessionId!;
        Logger.info('Created session: $sessionId');

        // Step 2: Execute comprehensive extraction
        final extractionResponse = await executeComprehensiveExtraction(
          sessionId: sessionId,
          intent: userIntent,
          tenantName: tenantName ?? 'Default Organization',
          businessDomain: businessDomain ?? 'General Business',
          context: context ?? 'Complete extraction workflow',
        );

        if (!extractionResponse.success) {
          throw Exception(
              extractionResponse.message ?? 'Failed to execute extraction');
        }

        Logger.info(
            'Completed comprehensive extraction for session: $sessionId');

        // Step 3: Get entities
        final entitiesResponse = await getSessionEntities(sessionId);
        if (!entitiesResponse.success) {
          throw Exception(
              entitiesResponse.message ?? 'Failed to fetch entities');
        }

        Logger.info(
            'Successfully completed workflow with ${entitiesResponse.data?.length ?? 0} entities');

        return ObjectCreationResponse(
          success: true,
          message: 'Complete extraction workflow completed successfully',
          data: entitiesResponse.data,
          sessionId: sessionId,
        );
      },
      operationName: 'executeCompleteExtractionWorkflowNew',
    );
  }
}
