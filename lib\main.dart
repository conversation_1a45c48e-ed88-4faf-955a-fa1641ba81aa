import 'package:flutter/material.dart';
import 'app.dart';
import 'config/environment.dart';
import 'services/service_locator.dart';
import 'utils/logger.dart';

void main() async {
  // Catch any errors that occur during initialization
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize loggers
    Logger.init();
    Logger.info('Starting application');

    // Initialize environment
    await Environment.init();
    Logger.info('Environment initialized');

    // Initialize service locator
    await ServiceLocator().init();
    Logger.info('Service locator initialized');

    runApp(const App());
  } catch (e) {
    // Log any errors that occur during initialization
    // Use print since Logger might not be initialized
    // ignore: avoid_print
    print('Fatal error during initialization: $e');
    // Show an error screen
    runApp(MaterialApp(
      home: Scaffold(
        body: Center(
          child: Text('Fatal error during initialization: $e'),
        ),
      ),
    ));
  }
}
