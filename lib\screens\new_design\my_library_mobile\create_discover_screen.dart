import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';

// Discover Screen
class DiscoverModalScreen extends StatelessWidget {
  const DiscoverModalScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      body: Column(
        children: [
          // Fixed Header with Close Button
          Container(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top + 16,
              left: 16,
              right: 16,
              bottom: 24,
            ),
            child: Row(
              children: [
                Expanded(child: SizedBox()),
                IconButton(
                  icon: Icon(Icons.close, color: Colors.black, size: 24),
                  onPressed: () => Navigator.of(context).pop(),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(),
                ),
              ],
            ),
          ),

          // Scrollable Content
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: 600, // Max width for content
                ),
                margin: EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 32),

                    // Title
                    Center(
                      child: Text(
                        'How Discover Works -\nYour AI-Guided Journey',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                          height: 1.3,
                        ),
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Steps
                    _buildStep(
                      stepNumber: '1',
                      title: 'Describe Your Solution',
                      description:
                          'Specify your business industry, organization size, operational requirements, and geographic locations for the solution.',
                    ),
                    const SizedBox(height: 32),
                    _buildStep(
                      stepNumber: '2',
                      title: 'AI Analysis',
                      description:
                          'Our AI engine automatically discovers and lists out the roles, entities, and workflows for your solution',
                    ),
                    const SizedBox(height: 32),
                    _buildStep(
                      stepNumber: '3',
                      title: 'Review & Customize',
                      description:
                          'Validate the document in details and customize any components you want to modify with prompt',
                    ),
                    const SizedBox(height: 32),
                    _buildStep(
                      stepNumber: '4',
                      title: 'Development',
                      description:
                          'Once you have confirmed the final components, proceed with the development of your solution using the discovered framework',
                    ),
                    const SizedBox(height: 32),
                    _buildStep(
                      stepNumber: '5',
                      title: 'Testing',
                      description:
                          'Test your completed solution to ensure it works as expected.',
                    ),

                    const SizedBox(height: 60), // Extra bottom spacing
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStep({
    required String stepNumber,
    required String title,
    required String description,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Step $stepNumber: $title',
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          description,
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: Colors.black87,
            height: 1.5,
          ),
        ),
      ],
    );
  }
}
