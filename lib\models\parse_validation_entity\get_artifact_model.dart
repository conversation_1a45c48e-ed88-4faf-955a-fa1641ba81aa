// To parse this JSON data, do
//
//     final getArtifactsModel = getArtifactsModelFromJson(jsonString);

import 'dart:convert';

import 'package:nsl/models/solution/go_model.dart';

GetArtifactsModel getArtifactsModelFromJson(String str) =>
    GetArtifactsModel.fromJson(json.decode(str));

String getArtifactsModelToJson(GetArtifactsModel data) =>
    json.encode(data.toJson());

class GetArtifactsModel {
  bool? success;
  List<Artifact>? artifacts;
  int? totalCount;
  int? returnedCount;
  int? skip;
  int? limit;
  String? tenantId;
  String? collectionName;
  FiltersApplied? filtersApplied;
  DateTime? timestamp;

  GetArtifactsModel({
    this.success,
    this.artifacts,
    this.totalCount,
    this.returnedCount,
    this.skip,
    this.limit,
    this.tenantId,
    this.collectionName,
    this.filtersApplied,
    this.timestamp,
  });

  GetArtifactsModel copyWith({
    bool? success,
    List<Artifact>? artifacts,
    int? totalCount,
    int? returnedCount,
    int? skip,
    int? limit,
    String? tenantId,
    String? collectionName,
    FiltersApplied? filtersApplied,
    DateTime? timestamp,
  }) =>
      GetArtifactsModel(
        success: success ?? this.success,
        artifacts: artifacts ?? this.artifacts,
        totalCount: totalCount ?? this.totalCount,
        returnedCount: returnedCount ?? this.returnedCount,
        skip: skip ?? this.skip,
        limit: limit ?? this.limit,
        tenantId: tenantId ?? this.tenantId,
        collectionName: collectionName ?? this.collectionName,
        filtersApplied: filtersApplied ?? this.filtersApplied,
        timestamp: timestamp ?? this.timestamp,
      );

  factory GetArtifactsModel.fromJson(Map<String, dynamic> json) =>
      GetArtifactsModel(
        success: json["success"],
        artifacts: json["artifacts"] == null
            ? []
            : List<Artifact>.from(
                json["artifacts"]!.map((x) => Artifact.fromJson(x))),
        totalCount: json["total_count"],
        returnedCount: json["returned_count"],
        skip: json["skip"],
        limit: json["limit"],
        tenantId: json["tenant_id"],
        collectionName: json["collection_name"],
        filtersApplied: json["filters_applied"] == null
            ? null
            : FiltersApplied.fromJson(json["filters_applied"]),
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "artifacts": artifacts == null
            ? []
            : List<dynamic>.from(artifacts!.map((x) => x.toJson())),
        "total_count": totalCount,
        "returned_count": returnedCount,
        "skip": skip,
        "limit": limit,
        "tenant_id": tenantId,
        "collection_name": collectionName,
        "filters_applied": filtersApplied?.toJson(),
        "timestamp": timestamp?.toIso8601String(),
      };
}

class Artifact {
  String? id;
  String? artifactId;
  String? tenantId;
  String? primaryArtifactType;
  String? artifactName;
  String? chatId;
  String? projectId;
  ParseValidateResponse? parseValidateResponse;

  List<Entity>? entity;
  List<GoModel>? go;
  List? role;
  List<String>? artifactTypesIncluded;
  String? notes;
  List<String>? tags;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;
  String? status;
  int? version;
  String? sourceId;
  String? sourceName;
  bool? isValid;

  Artifact({
    this.id,
    this.artifactId,
    this.tenantId,
    this.primaryArtifactType,
    this.artifactName,
    this.chatId,
    this.projectId,
    this.parseValidateResponse,
    this.entity,
    this.artifactTypesIncluded,
    this.notes,
    this.tags,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.status,
    this.version,
    this.sourceId,
    this.sourceName,
    this.isValid,
  });

  Artifact copyWith({
    String? id,
    String? artifactId,
    String? tenantId,
    String? primaryArtifactType,
    String? artifactName,
    String? chatId,
    String? projectId,
    ParseValidateResponse? parseValidateResponse,
    List<Entity>? entity,
    List<String>? artifactTypesIncluded,
    String? notes,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    String? status,
    int? version,
    String? sourceId,
    String? sourceName,
    bool? isValid,
  }) =>
      Artifact(
        id: id ?? this.id,
        artifactId: artifactId ?? this.artifactId,
        tenantId: tenantId ?? this.tenantId,
        primaryArtifactType: primaryArtifactType ?? this.primaryArtifactType,
        artifactName: artifactName ?? this.artifactName,
        chatId: chatId ?? this.chatId,
        projectId: projectId ?? this.projectId,
        parseValidateResponse:
            parseValidateResponse ?? this.parseValidateResponse,
        entity: entity ?? this.entity,
        artifactTypesIncluded:
            artifactTypesIncluded ?? this.artifactTypesIncluded,
        notes: notes ?? this.notes,
        tags: tags ?? this.tags,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        status: status ?? this.status,
        version: version ?? this.version,
        sourceId: sourceId ?? this.sourceId,
        sourceName: sourceName ?? this.sourceName,
        isValid: isValid ?? this.isValid,
      );

  factory Artifact.fromJson(Map<String, dynamic> json) => Artifact(
        id: json["_id"],
        artifactId: json["artifact_id"],
        tenantId: json["tenant_id"],
        primaryArtifactType: json["primary_artifact_type"],
        artifactName: json["artifact_name"],
        chatId: json["chat_id"],
        projectId: json["project_id"],
        parseValidateResponse: json["parse_validate_response"] == null
            ? null
            : ParseValidateResponse.fromJson(json["parse_validate_response"]),
        entity: json["entity"] == null
            ? []
            : List<Entity>.from(json["entity"]!.map((x) => Entity.fromJson(x))),
        artifactTypesIncluded: json["artifact_types_included"] == null
            ? []
            : List<String>.from(json["artifact_types_included"]!.map((x) => x)),
        notes: json["notes"],
        tags: json["tags"] == null
            ? []
            : List<String>.from(json["tags"]!.map((x) => x)),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        status: json["status"],
        version: json["version"],
        sourceId: json["source_id"],
        sourceName: json["source_name"],
        isValid: json["is_valid"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "artifact_id": artifactId,
        "tenant_id": tenantId,
        "primary_artifact_type": primaryArtifactType,
        "artifact_name": artifactName,
        "chat_id": chatId,
        "project_id": projectId,
        "parse_validate_response": parseValidateResponse?.toJson(),
        "entity": entity == null
            ? []
            : List<dynamic>.from(entity!.map((x) => x.toJson())),
        "artifact_types_included": artifactTypesIncluded == null
            ? []
            : List<dynamic>.from(artifactTypesIncluded!.map((x) => x)),
        "notes": notes,
        "tags": tags == null ? [] : List<dynamic>.from(tags!.map((x) => x)),
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_by": updatedBy,
        "status": status,
        "version": version,
        "source_id": sourceId,
        "source_name": sourceName,
        "is_valid": isValid,
      };
}

class Entity {
  String? subArtifactId;
  String? artifactType;
  String? artifactName;
  ParseValidateResponse? parseValidateResponse;
  String? notes;
  List<String>? tags;
  DateTime? createdAt;
  String? createdBy;

  Entity({
    this.subArtifactId,
    this.artifactType,
    this.artifactName,
    this.parseValidateResponse,
    this.notes,
    this.tags,
    this.createdAt,
    this.createdBy,
  });

  Entity copyWith({
    String? subArtifactId,
    String? artifactType,
    String? artifactName,
    ParseValidateResponse? parseValidateResponse,
    String? notes,
    List<String>? tags,
    DateTime? createdAt,
    String? createdBy,
  }) =>
      Entity(
        subArtifactId: subArtifactId ?? this.subArtifactId,
        artifactType: artifactType ?? this.artifactType,
        artifactName: artifactName ?? this.artifactName,
        parseValidateResponse:
            parseValidateResponse ?? this.parseValidateResponse,
        notes: notes ?? this.notes,
        tags: tags ?? this.tags,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
      );

  factory Entity.fromJson(Map<String, dynamic> json) => Entity(
        subArtifactId: json["sub_artifact_id"],
        artifactType: json["artifact_type"],
        artifactName: json["artifact_name"],
        parseValidateResponse: json["parse_validate_response"] == null
            ? null
            : ParseValidateResponse.fromJson(json["parse_validate_response"]),
        notes: json["notes"],
        tags: json["tags"] == null
            ? []
            : List<String>.from(json["tags"]!.map((x) => x)),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        createdBy: json["created_by"],
      );

  Map<String, dynamic> toJson() => {
        "sub_artifact_id": subArtifactId,
        "artifact_type": artifactType,
        "artifact_name": artifactName,
        "parse_validate_response": parseValidateResponse?.toJson(),
        "notes": notes,
        "tags": tags == null ? [] : List<dynamic>.from(tags!.map((x) => x)),
        "created_at": createdAt?.toIso8601String(),
        "created_by": createdBy,
      };
}

class ParseValidateResponse {
  bool? success;
  ParsedData? parsedData;
  bool? isValid;
  List<dynamic>? validationErrors;

  ParseValidateResponse({
    this.success,
    this.parsedData,
    this.isValid,
    this.validationErrors,
  });

  ParseValidateResponse copyWith({
    bool? success,
    ParsedData? parsedData,
    bool? isValid,
    List<dynamic>? validationErrors,
  }) =>
      ParseValidateResponse(
        success: success ?? this.success,
        parsedData: parsedData ?? this.parsedData,
        isValid: isValid ?? this.isValid,
        validationErrors: validationErrors ?? this.validationErrors,
      );

  factory ParseValidateResponse.fromJson(Map<String, dynamic> json) =>
      ParseValidateResponse(
        success: json["success"],
        parsedData: json["parsed_data"] == null
            ? null
            : ParsedData.fromJson(json["parsed_data"]),
        isValid: json["is_valid"],
        validationErrors: json["validation_errors"] == null
            ? []
            : List<dynamic>.from(json["validation_errors"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "parsed_data": parsedData?.toJson(),
        "is_valid": isValid,
        "validation_errors": validationErrors == null
            ? []
            : List<dynamic>.from(validationErrors!.map((x) => x)),
      };
}

class ParsedData {
  dynamic tenant;
  dynamic entityDeclaration;
  String? id;
  String? name;
  String? displayName;
  String? type;
  String? description;
  dynamic businessPurpose;
  dynamic businessDomain;
  dynamic category;
  dynamic archivalStrategy;
  dynamic colorTheme;
  String? icon;
  dynamic tags;
  List<dynamic>? attributes;
  List<dynamic>? relationships;
  dynamic businessRules;
  List<dynamic>? enumValues;
  dynamic securityClassification;
  dynamic systemPermissions;
  dynamic roleSystemPermissions;
  dynamic uiProperties;
  dynamic confidence;
  dynamic extractionMethod;
  dynamic completionScore;
  dynamic configurationStatus;
  dynamic createdAt;
  dynamic updatedAt;
  bool? isEntityValidatedSaved;
  bool? isEntityAttributesValidatedSaved;
  bool? isEntityRelationshipsValidatedSaved;
  bool? isEntityBusinessRulesValidatedSaved;
  bool? isEntityEnumValuesValidatedSaved;
  bool? isEntitySecurityClassificationValidatedSaved;
  bool? isEntitySystemPropertiesValidatedSaved;
  bool? attributesExpanded;
  bool? relationshipsExpanded;
  bool? enumValuesExpanded;

  ParsedData({
    this.tenant,
    this.entityDeclaration,
    this.id,
    this.name,
    this.displayName,
    this.type,
    this.description,
    this.businessPurpose,
    this.businessDomain,
    this.category,
    this.archivalStrategy,
    this.colorTheme,
    this.icon,
    this.tags,
    this.attributes,
    this.relationships,
    this.businessRules,
    this.enumValues,
    this.securityClassification,
    this.systemPermissions,
    this.roleSystemPermissions,
    this.uiProperties,
    this.confidence,
    this.extractionMethod,
    this.completionScore,
    this.configurationStatus,
    this.createdAt,
    this.updatedAt,
    this.isEntityValidatedSaved,
    this.isEntityAttributesValidatedSaved,
    this.isEntityRelationshipsValidatedSaved,
    this.isEntityBusinessRulesValidatedSaved,
    this.isEntityEnumValuesValidatedSaved,
    this.isEntitySecurityClassificationValidatedSaved,
    this.isEntitySystemPropertiesValidatedSaved,
    this.attributesExpanded,
    this.relationshipsExpanded,
    this.enumValuesExpanded,
  });

  ParsedData copyWith({
    dynamic tenant,
    dynamic entityDeclaration,
    String? id,
    String? name,
    String? displayName,
    String? type,
    String? description,
    dynamic businessPurpose,
    dynamic businessDomain,
    dynamic category,
    dynamic archivalStrategy,
    dynamic colorTheme,
    String? icon,
    dynamic tags,
    List<dynamic>? attributes,
    List<dynamic>? relationships,
    dynamic businessRules,
    List<dynamic>? enumValues,
    dynamic securityClassification,
    dynamic systemPermissions,
    dynamic roleSystemPermissions,
    dynamic uiProperties,
    dynamic confidence,
    dynamic extractionMethod,
    dynamic completionScore,
    dynamic configurationStatus,
    dynamic createdAt,
    dynamic updatedAt,
    bool? isEntityValidatedSaved,
    bool? isEntityAttributesValidatedSaved,
    bool? isEntityRelationshipsValidatedSaved,
    bool? isEntityBusinessRulesValidatedSaved,
    bool? isEntityEnumValuesValidatedSaved,
    bool? isEntitySecurityClassificationValidatedSaved,
    bool? isEntitySystemPropertiesValidatedSaved,
    bool? attributesExpanded,
    bool? relationshipsExpanded,
    bool? enumValuesExpanded,
  }) =>
      ParsedData(
        tenant: tenant ?? this.tenant,
        entityDeclaration: entityDeclaration ?? this.entityDeclaration,
        id: id ?? this.id,
        name: name ?? this.name,
        displayName: displayName ?? this.displayName,
        type: type ?? this.type,
        description: description ?? this.description,
        businessPurpose: businessPurpose ?? this.businessPurpose,
        businessDomain: businessDomain ?? this.businessDomain,
        category: category ?? this.category,
        archivalStrategy: archivalStrategy ?? this.archivalStrategy,
        colorTheme: colorTheme ?? this.colorTheme,
        icon: icon ?? this.icon,
        tags: tags ?? this.tags,
        attributes: attributes ?? this.attributes,
        relationships: relationships ?? this.relationships,
        businessRules: businessRules ?? this.businessRules,
        enumValues: enumValues ?? this.enumValues,
        securityClassification:
            securityClassification ?? this.securityClassification,
        systemPermissions: systemPermissions ?? this.systemPermissions,
        roleSystemPermissions:
            roleSystemPermissions ?? this.roleSystemPermissions,
        uiProperties: uiProperties ?? this.uiProperties,
        confidence: confidence ?? this.confidence,
        extractionMethod: extractionMethod ?? this.extractionMethod,
        completionScore: completionScore ?? this.completionScore,
        configurationStatus: configurationStatus ?? this.configurationStatus,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        isEntityValidatedSaved:
            isEntityValidatedSaved ?? this.isEntityValidatedSaved,
        isEntityAttributesValidatedSaved: isEntityAttributesValidatedSaved ??
            this.isEntityAttributesValidatedSaved,
        isEntityRelationshipsValidatedSaved:
            isEntityRelationshipsValidatedSaved ??
                this.isEntityRelationshipsValidatedSaved,
        isEntityBusinessRulesValidatedSaved:
            isEntityBusinessRulesValidatedSaved ??
                this.isEntityBusinessRulesValidatedSaved,
        isEntityEnumValuesValidatedSaved: isEntityEnumValuesValidatedSaved ??
            this.isEntityEnumValuesValidatedSaved,
        isEntitySecurityClassificationValidatedSaved:
            isEntitySecurityClassificationValidatedSaved ??
                this.isEntitySecurityClassificationValidatedSaved,
        isEntitySystemPropertiesValidatedSaved:
            isEntitySystemPropertiesValidatedSaved ??
                this.isEntitySystemPropertiesValidatedSaved,
        attributesExpanded: attributesExpanded ?? this.attributesExpanded,
        relationshipsExpanded:
            relationshipsExpanded ?? this.relationshipsExpanded,
        enumValuesExpanded: enumValuesExpanded ?? this.enumValuesExpanded,
      );

  factory ParsedData.fromJson(Map<String, dynamic> json) => ParsedData(
        tenant: json["tenant"],
        entityDeclaration: json["entityDeclaration"],
        id: json["id"],
        name: json["name"],
        displayName: json["displayName"],
        type: json["type"],
        description: json["description"],
        businessPurpose: json["businessPurpose"],
        businessDomain: json["businessDomain"],
        category: json["category"],
        archivalStrategy: json["archivalStrategy"],
        colorTheme: json["colorTheme"],
        icon: json["icon"],
        tags: json["tags"],
        attributes: json["attributes"] == null
            ? []
            : List<dynamic>.from(json["attributes"]!.map((x) => x)),
        relationships: json["relationships"] == null
            ? []
            : List<dynamic>.from(json["relationships"]!.map((x) => x)),
        businessRules: json["business_rules"],
        enumValues: json["enum_values"] == null
            ? []
            : List<dynamic>.from(json["enum_values"]!.map((x) => x)),
        securityClassification: json["security_classification"],
        systemPermissions: json["system_permissions"],
        roleSystemPermissions: json["role_system_permissions"],
        uiProperties: json["uiProperties"],
        confidence: json["confidence"],
        extractionMethod: json["extraction_method"],
        completionScore: json["completion_score"],
        configurationStatus: json["configuration_status"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        isEntityValidatedSaved: json["isEntityValidatedSaved"],
        isEntityAttributesValidatedSaved:
            json["isEntityAttributesValidatedSaved"],
        isEntityRelationshipsValidatedSaved:
            json["isEntityRelationshipsValidatedSaved"],
        isEntityBusinessRulesValidatedSaved:
            json["isEntityBusinessRulesValidatedSaved"],
        isEntityEnumValuesValidatedSaved:
            json["isEntityEnumValuesValidatedSaved"],
        isEntitySecurityClassificationValidatedSaved:
            json["isEntitySecurityClassificationValidatedSaved"],
        isEntitySystemPropertiesValidatedSaved:
            json["isEntitySystemPropertiesValidatedSaved"],
        attributesExpanded: json["attributesExpanded"],
        relationshipsExpanded: json["relationshipsExpanded"],
        enumValuesExpanded: json["enumValuesExpanded"],
      );

  Map<String, dynamic> toJson() => {
        "tenant": tenant,
        "entityDeclaration": entityDeclaration,
        "id": id,
        "name": name,
        "displayName": displayName,
        "type": type,
        "description": description,
        "businessPurpose": businessPurpose,
        "businessDomain": businessDomain,
        "category": category,
        "archivalStrategy": archivalStrategy,
        "colorTheme": colorTheme,
        "icon": icon,
        "tags": tags,
        "attributes": attributes == null
            ? []
            : List<dynamic>.from(attributes!.map((x) => x)),
        "relationships": relationships == null
            ? []
            : List<dynamic>.from(relationships!.map((x) => x)),
        "business_rules": businessRules,
        "enum_values": enumValues == null
            ? []
            : List<dynamic>.from(enumValues!.map((x) => x)),
        "security_classification": securityClassification,
        "system_permissions": systemPermissions,
        "role_system_permissions": roleSystemPermissions,
        "uiProperties": uiProperties,
        "confidence": confidence,
        "extraction_method": extractionMethod,
        "completion_score": completionScore,
        "configuration_status": configurationStatus,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "isEntityValidatedSaved": isEntityValidatedSaved,
        "isEntityAttributesValidatedSaved": isEntityAttributesValidatedSaved,
        "isEntityRelationshipsValidatedSaved":
            isEntityRelationshipsValidatedSaved,
        "isEntityBusinessRulesValidatedSaved":
            isEntityBusinessRulesValidatedSaved,
        "isEntityEnumValuesValidatedSaved": isEntityEnumValuesValidatedSaved,
        "isEntitySecurityClassificationValidatedSaved":
            isEntitySecurityClassificationValidatedSaved,
        "isEntitySystemPropertiesValidatedSaved":
            isEntitySystemPropertiesValidatedSaved,
        "attributesExpanded": attributesExpanded,
        "relationshipsExpanded": relationshipsExpanded,
        "enumValuesExpanded": enumValuesExpanded,
      };
}

class FiltersApplied {
  String? artifactType;
  dynamic chatId;
  dynamic projectId;
  dynamic status;

  FiltersApplied({
    this.artifactType,
    this.chatId,
    this.projectId,
    this.status,
  });

  FiltersApplied copyWith({
    String? artifactType,
    dynamic chatId,
    dynamic projectId,
    dynamic status,
  }) =>
      FiltersApplied(
        artifactType: artifactType ?? this.artifactType,
        chatId: chatId ?? this.chatId,
        projectId: projectId ?? this.projectId,
        status: status ?? this.status,
      );

  factory FiltersApplied.fromJson(Map<String, dynamic> json) => FiltersApplied(
        artifactType: json["artifact_type"],
        chatId: json["chat_id"],
        projectId: json["project_id"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "artifact_type": artifactType,
        "chat_id": chatId,
        "project_id": projectId,
        "status": status,
      };
}
