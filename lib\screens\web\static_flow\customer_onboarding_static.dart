import 'package:flutter/material.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/models/object_creation_model.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/theme/spacing.dart';

import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:provider/provider.dart';

import '../../../models/customer_model.dart';

// Import extracted components
import 'customer_onboarding/constants/customer_onboarding_constants.dart';
import 'customer_onboarding/widgets/accordion_item_widget.dart';

class CustomerOnboardingStatic extends StatefulWidget {
  final List<ObjectCreationModel>? objects;

  const CustomerOnboardingStatic({
    super.key,
    this.objects,
  });

  @override
  State<CustomerOnboardingStatic> createState() =>
      _CustomerOnboardingStaticState();
}

class _CustomerOnboardingStaticState extends State<CustomerOnboardingStatic> {
  late AccordionController _accordionController;
  late List<BusinessRule1> _businessRulesData;
  bool _showProcessSteps = false; // Track Level 1 vs Level 2 view

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
    _businessRulesData =
        List.from(CustomerOnboardingConstants.defaultBusinessRules);

    _accordionController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _accordionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildAdvancedObjectExpansionPanel(
          context,
          'Solution: Customer Onboarding',
          objectData: null,
        ),
      ],
    );
  }

  void updateRuleByField(String field, BusinessRule1 updatedRule) {
    final index = _businessRulesData.indexWhere((rule) => rule.field == field);
    if (index != -1) {
      _businessRulesData[index] = updatedRule;
      setState(() {});
    }
  }

  Widget _buildAdvancedObjectExpansionPanel(
      BuildContext context, String objectTitle,
      {ObjectCreationModel? objectData}) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        final isExpanded = provider.isObjectExpanded(objectTitle);

        return Container(
          margin: EdgeInsets.symmetric(vertical: AppSpacing.xxs / 4),
          decoration: const BoxDecoration(
            color: Colors.white,
          ),
          child: Theme(
            data: Theme.of(context).copyWith(
              dividerColor: Colors.transparent,
            ),
            child: ListTileTheme(
              dense: true,
              child: ExpansionTile(
                tilePadding:
                    const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                childrenPadding: EdgeInsets.zero,
                onExpansionChanged: (expanded) =>
                    provider.setObjectExpansion(objectTitle, expanded),
                trailing: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isExpanded
                        ? const Color(0xFF0058FF)
                        : Colors.transparent,
                  ),
                  child: Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: isExpanded ? Colors.white : Colors.grey[600],
                    size: 20,
                  ),
                ),
                title: Row(
                  children: [
                    Expanded(
                      child: Text(
                        objectTitle,
                        style: FontManager.getCustomStyle(
                          fontSize: _getResponsiveValueFontSize(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: isExpanded
                              ? Colors.black
                              : const Color(0xFF242424),
                          fontWeight:
                              isExpanded ? FontWeight.w600 : FontWeight.w400,
                          height: 1.2,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    _HoverBellIcon(
                      onTap: () {
                        // Bell icon click action - can be customized as needed
                      },
                    ),
                  ],
                ),
                children: [
                  _buildSolutionDetailsSection(context, objectTitle,
                      objectData: objectData),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSolutionDetailsSection(BuildContext context, String objectTitle,
      {ObjectCreationModel? objectData}) {
    return Container(
      margin: const EdgeInsets.fromLTRB(12, 0, 4, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Level 1: Customer Onboarding subheading (clickable) - styled like ExpansionTile
          Container(
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: InkWell(
              onTap: () {
                setState(() {
                  _showProcessSteps = !_showProcessSteps;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Customer Onboarding',
                        style: FontManager.getCustomStyle(
                          fontSize: _getResponsiveValueFontSize(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: _showProcessSteps
                              ? Colors.black
                              : const Color(0xFF242424),
                          fontWeight: _showProcessSteps
                              ? FontWeight.w600
                              : FontWeight.w400,
                          height: 1.2,
                        ),
                      ),
                    ),
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: _showProcessSteps
                            ? const Color(0xFF0058FF)
                            : Colors.transparent,
                      ),
                      child: Icon(
                        _showProcessSteps
                            ? Icons.keyboard_arrow_up
                            : Icons.keyboard_arrow_down,
                        color:
                            _showProcessSteps ? Colors.white : Colors.grey[600],
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Level 1 or Level 2 content based on state
          if (_showProcessSteps)
            _buildProcessStepsView(objectData)
          else
            _buildStaticRulesView(),
        ],
      ),
    );
  }

  /// Level 1: Build static rules view (8 text points)
  Widget _buildStaticRulesView() {
    const List<String> staticRules = [
      'System Verifies Budget Availability.',
      'Requester Creates Purchase Requisition.',
      'System Validates Purchase Requisition.',
      'Director Reviews High-Value Purchase Requisition.',
      'Procurement Officer Creates Purchase Order.',
      'System Initiates Parallel Assessment Tasks.',
      'Manager Reviews Purchase Requisition.',
      'System Notifies Requester Of Rejection.',
    ];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: staticRules.asMap().entries.map((entry) {
          final index = entry.key;
          final rule = entry.value;
          return Padding(
            padding: EdgeInsets.symmetric(vertical: AppSpacing.xxs),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${index + 1}.',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: AppSpacing.xs),
                Expanded(
                  child: Text(
                    rule,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black87,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Level 2: Build process steps view (expansion panels)
  Widget _buildProcessStepsView(ObjectCreationModel? objectData) {
    return Column(
      children: CustomerOnboardingConstants.processSteps.map((step) {
        return AccordionItemWidget(
          title: step['title'] as String,
          showAttributeTable: step['showAttributeTable'] as bool,
          accordionController: _accordionController,
          objectData: objectData,
          businessRulesData:
              step['title'] == 'Core Metadata' ? _businessRulesData : null,
          onUpdateRule: updateRuleByField,
          isExpandEnabled: true,
          isSaved: false,
          // Let the widget auto-determine status based on title
        );
      }).toList(),
    );
  }

  /// Get responsive font size based on screen width
  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium
    } else {
      return 12.0; // Default for very small screens
    }
  }
}

// Advanced HoverBellIcon component that matches extract_details_middle_static.dart implementation
class _HoverBellIcon extends StatefulWidget {
  final VoidCallback onTap;

  const _HoverBellIcon({
    required this.onTap,
  });

  @override
  State<_HoverBellIcon> createState() => _HoverBellIconState();
}

class _HoverBellIconState extends State<_HoverBellIcon> {
  bool isHovered = false;
  OverlayEntry? _overlayEntry;
  final GlobalKey _bellIconKey = GlobalKey();
  bool _isOverPopup = false;

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _showOverlay() {
    if (_overlayEntry != null) return;

    final RenderBox? renderBox =
        _bellIconKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx - 280, // Adjust horizontal position
        top: position.dy + size.height + 8, // Position below the bell icon
        child: MouseRegion(
          onEnter: (_) {
            _isOverPopup = true;
          },
          onExit: (_) {
            _isOverPopup = false;
            // Small delay to check if we're still hovering
            Future.delayed(const Duration(milliseconds: 50), () {
              if (mounted && !isHovered && !_isOverPopup) {
                _removeOverlay();
              }
            });
          },
          child: Material(
            color: Colors.transparent,
            child: _buildHoverPopup(),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _handleMouseExit() {
    setState(() => isHovered = false);
    // Small delay to allow moving to popup
    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted && !isHovered && !_isOverPopup) {
        _removeOverlay();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() => isHovered = true);
        _showOverlay();
      },
      onExit: (_) {
        _handleMouseExit();
      },
      child: InkWell(
        onTap: widget.onTap,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: Icon(
            key: _bellIconKey,
            Icons.notifications_outlined,
            size: 18,
            color: const Color(0xffFF2019),
          ),
        ),
      ),
    );
  }

  Widget _buildHoverPopup() {
    return Container(
      width: 300,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Message text
          Text(
            'This Objects is already exists in your library. You need to rename the Objects to proceed.',
            textAlign: TextAlign.center,
            style: FontManager.getCustomStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 20),
          // Buttons row
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Continue button (outlined)
              SizedBox(
                height: 32,
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      isHovered = false;
                      _isOverPopup = false;
                    });
                    _removeOverlay();
                    // Handle continue action
                  },
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Colors.grey.shade400, width: 1),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    backgroundColor: Colors.white,
                  ),
                  child: Text(
                    'Continue',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      color: Colors.black87,
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // Resolve button (filled)
              SizedBox(
                height: 32,
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      isHovered = false;
                      _isOverPopup = false;
                    });
                    _removeOverlay();
                    // Handle resolve action
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xff007AFF),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    elevation: 0,
                  ),
                  child: Text(
                    'Resolve',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
