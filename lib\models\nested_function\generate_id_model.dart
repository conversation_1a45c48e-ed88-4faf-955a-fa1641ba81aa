class GenerateIdModel {
  String? functionType;
  String? functionName;

  // Entity and Attribute - separate entity and attribute
  GenerateIdEntityAttribute? entity;
  GenerateIdEntityAttribute? attribute;
  GenerateIdEntityAttribute? idType;
  GenerateIdEntityAttribute? uuidSequenceTimestamp;
  GenerateIdEntityAttribute? prefix;
  GenerateIdEntityAttribute? suffix;
  GenerateIdEntityAttribute? length;

  // 🆕 Dropdown selections
  String? source;

  GenerateIdModel({
    this.functionType,
    this.functionName,
    this.entity,
    this.attribute,
    this.idType,
    this.uuidSequenceTimestamp,
    this.prefix,
    this.suffix,
    this.length,
    this.source,
  });

  GenerateIdModel copyWith({
    String? functionType,
    String? functionName,
    GenerateIdEntityAttribute? entity,
    GenerateIdEntityAttribute? attribute,
    GenerateIdEntityAttribute? idType,
    GenerateIdEntityAttribute? uuidSequenceTimestamp,
    GenerateIdEntityAttribute? prefix,
    GenerateIdEntityAttribute? suffix,
    GenerateIdEntityAttribute? length,
    String? source,
    String? popupDropdownSelection,
    List<String>? nestedDropdownValues,
  }) =>
      GenerateIdModel(
        functionType: functionType ?? this.functionType,
        functionName: functionName ?? this.functionName,
        entity: entity ?? this.entity,
        attribute: attribute ?? this.attribute,
        idType: idType ?? this.idType,
        uuidSequenceTimestamp: uuidSequenceTimestamp ?? this.uuidSequenceTimestamp,
        prefix: prefix ?? this.prefix,
        suffix: suffix ?? this.suffix,
        length: length ?? this.length,
        source: source ?? this.source,
      );

  factory GenerateIdModel.fromJson(Map<String, dynamic> json) => GenerateIdModel(
        functionType: json["functionType"],
        functionName: json["functionName"],
        entity: json["entity"] != null ? GenerateIdEntityAttribute.fromJson(json["entity"]) : null,
        attribute: json["attribute"] != null ? GenerateIdEntityAttribute.fromJson(json["attribute"]) : null,
        idType: json["idType"] != null ? GenerateIdEntityAttribute.fromJson(json["idType"]) : null,
        uuidSequenceTimestamp: json["uuidSequenceTimestamp"] != null ? GenerateIdEntityAttribute.fromJson(json["uuidSequenceTimestamp"]) : null,
        prefix: json["prefix"] != null ? GenerateIdEntityAttribute.fromJson(json["prefix"]) : null,
        suffix: json["suffix"] != null ? GenerateIdEntityAttribute.fromJson(json["suffix"]) : null,
        length: json["length"] != null ? GenerateIdEntityAttribute.fromJson(json["length"]) : null,
        source: json["source"],
      );

  Map<String, dynamic> toJson() => {
        "functionType": functionType,
        "functionName": functionName,
        "entity": entity?.toJson(),
        "attribute": attribute?.toJson(),
        "idType": idType?.toJson(),
        "uuidSequenceTimestamp": uuidSequenceTimestamp?.toJson(),
        "prefix": prefix?.toJson(),
        "suffix": suffix?.toJson(),
        "length": length?.toJson(),
        "source": source,
      };
}

class GenerateIdEntityAttribute {
  String? entityName;
  String? attributeName;

  GenerateIdEntityAttribute({
    this.entityName,
    this.attributeName,
  });

  GenerateIdEntityAttribute copyWith({
    String? entityName,
    String? attributeName,
  }) =>
      GenerateIdEntityAttribute(
        entityName: entityName ?? this.entityName,
        attributeName: attributeName ?? this.attributeName,
      );

  factory GenerateIdEntityAttribute.fromJson(Map<String, dynamic> json) => GenerateIdEntityAttribute(
        entityName: json["entityName"],
        attributeName: json["attributeName"],
      );

  Map<String, dynamic> toJson() => {
        "entityName": entityName,
        "attributeName": attributeName,
      };
}


