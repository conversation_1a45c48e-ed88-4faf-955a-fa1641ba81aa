import 'package:flutter/material.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:provider/provider.dart';

class CreateSolutionOnboardingDetailsMobile extends StatefulWidget {
  const CreateSolutionOnboardingDetailsMobile({super.key});

  @override
  State<CreateSolutionOnboardingDetailsMobile> createState() =>
      _CreateSolutionOnboardingDetailsMobileState();
}

class _CreateSolutionOnboardingDetailsMobileState
    extends State<CreateSolutionOnboardingDetailsMobile> {
  final TextEditingController _solutionController = TextEditingController(
    text: 'Customer Onboarding',
  );
  final TextEditingController _descriptionController = TextEditingController(
    text:
        'Comprehensive customer onboarding process from registration to welcome completion',
  );
  final TextEditingController _localObjectiveController =
      TextEditingController();
  String? _selectedRole;
  bool _showExpansionPanels = false;

  final List<String> _roleOptions = [
    'Admin',
    'Manager',
    'User',
    'Guest',
    'Developer',
  ];

  @override
  void dispose() {
    _solutionController.dispose();
    _descriptionController.dispose();
    _localObjectiveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ObjectCreationProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: SafeArea(
            child: Column(
              children: [
                // Header
                _buildHeader(context),

                // Content area
                Expanded(child: _buildContent(context, provider)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1)),
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back, color: Colors.white, size: 24),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 8),
          // Title
          Expanded(
            child: Text(
              'Solution: Customer Onboarding',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.white,
                fontWeight: FontWeight.bold,
                height: 1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, ObjectCreationProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
          physics: const AlwaysScrollableScrollPhysics(),
        ),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - 200,
            ),
            child: _buildContentWithLineNumbers(context, provider),
          ),
        ),
      ),
    );
  }

  Widget _buildContentWithLineNumbers(
    BuildContext context,
    ObjectCreationProvider provider,
  ) {
    int lineNumber = 1;
    final List<Widget> allWidgets = [];

    if (!_showExpansionPanels) {
      // Line 1: Solution title
      allWidgets.add(_buildLineWithNumber(lineNumber++, _buildSolutionTitle()));

      // Line 2: Solution input field
      allWidgets.add(_buildLineWithNumber(lineNumber++, _buildSolutionField()));

      // Line 3: Roles dropdown
      allWidgets.add(_buildLineWithNumber(lineNumber++, _buildRolesField()));

      // Line 4: Description field
      allWidgets.add(
        _buildLineWithNumber(lineNumber++, _buildDescriptionField()),
      );

      // Local Objectives section header with line number 5
      allWidgets.add(_buildLocalObjectivesHeaderWithNumber());

      // Local Objective input field (without line number)
      allWidgets.add(_buildLocalObjectiveFieldWithoutNumber());

      // Validate button (without line number)
      allWidgets.add(_buildValidateButton(provider));
    } else {
      // Show expansion panels after validation
      allWidgets.add(
        _buildLineWithNumber(lineNumber++, _buildSolutionHeader()),
      );
      allWidgets.add(const SizedBox(height: 16));

      // Agent Type section
      allWidgets.add(
        _buildLineWithNumber(lineNumber++, _buildAgentTypeSection()),
      );
      allWidgets.add(const SizedBox(height: 12));

      // Description section
      allWidgets.add(
        _buildLineWithNumber(lineNumber++, _buildDescriptionSection()),
      );
      allWidgets.add(const SizedBox(height: 12));

      // Local Objectives section with expansion panels
      allWidgets.add(
        _buildLineWithNumber(lineNumber++, _buildLocalObjectivesSection()),
      );
      allWidgets.add(const SizedBox(height: 8));

      // Individual expansion panels with line numbers
      allWidgets.add(
        _buildLineWithNumber(
            lineNumber++,
            _buildObjectiveExpansionTile(
              'LO.1. System verifies budget availability.',
              true,
            )),
      );
      allWidgets.add(const SizedBox(height: 4));

      allWidgets.add(
        _buildLineWithNumber(
            lineNumber++,
            _buildObjectiveExpansionTile(
              'LO.2. Requestor creates Purchase Requisition.',
              false,
            )),
      );
      allWidgets.add(const SizedBox(height: 4));

      allWidgets.add(
        _buildLineWithNumber(
            lineNumber++,
            _buildObjectiveExpansionTile(
              'LO.3. System validates Purchase Requisition.',
              false,
            )),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: allWidgets,
    );
  }

  Widget _buildLineWithNumber(int lineNumber, Widget content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Line number
        Container(
          width: 20,
          child: Text(
            '$lineNumber',
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: Color(0xFF808080),
              fontFamily: FontManager.fontFamilyInter,
            ),
          ),
        ),
        // Space for the continuous vertical line
        const SizedBox(width: 17), // 8px margin + 1px line + 8px margin
        // Content
        Expanded(child: content),
      ],
    );
  }

  Widget _buildSolutionTitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Solution: Customer Onboarding',
              style: TextStyle(
                fontSize: ResponsiveFontSizes.bodyLarge(context),
                fontWeight: FontWeight.w700,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
            const SizedBox(width: 8),
            GestureDetector(
              onTap: () {
                _showInfoPopup(context);
              },
              child: Container(
                width: 16,
                height: 16,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: Colors.black,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12), // Bottom spacing
      ],
    );
  }

  Widget _buildSolutionField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Solution',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 40,
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFF7F7F7F), width: 0.5),
              borderRadius: BorderRadius.circular(6),
            ),
            alignment: Alignment.center,
            child: TextField(
              //controller: _solutionController,
              decoration: const InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding: EdgeInsets.symmetric(horizontal: 12),
                isDense: true,
              ),
              style: TextStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRolesField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Roles',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFF7F7F7F), width: 0.5),
              borderRadius: BorderRadius.circular(6),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _selectedRole,
                hint: Text(
                  'Select Type',
                  style: TextStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                icon: Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.grey.shade600,
                ),
                isExpanded: true,
                items: _roleOptions.map((String role) {
                  return DropdownMenuItem<String>(
                    value: role,
                    child: Text(
                      role,
                      style: TextStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedRole = newValue;
                  });
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Description:',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 120,
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFF7F7F7F), width: 0.5),
              borderRadius: BorderRadius.circular(6),
            ),
            child: TextField(
              controller: _descriptionController,
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
              decoration: InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding: const EdgeInsets.all(12),
                hintStyle: TextStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              style: TextStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocalObjectivesHeader() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        'LOCAL OBJECTIVES',
        style: TextStyle(
          fontSize: ResponsiveFontSizes.bodyLarge(context),
          fontWeight: FontWeight.w700,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
      ),
    );
  }

  Widget _buildLocalObjectiveField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFF7F7F7F), width: 0.5),
          borderRadius: BorderRadius.circular(6),
        ),
        alignment: Alignment.center,
        child: TextField(
          controller: _localObjectiveController,
          decoration: InputDecoration(
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            fillColor: Colors.transparent,
            hoverColor: Colors.transparent,
            contentPadding: const EdgeInsets.symmetric(horizontal: 12),
            hintText: 'Type LO name with full step (:)',
            hintStyle: TextStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey.shade600,
            ),
            isDense: true,
          ),
          style: TextStyle(
            fontSize: ResponsiveFontSizes.titleMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  Widget _buildLocalObjectivesHeaderWithNumber() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Line number 5
        Container(
          width: 20,
          child: Text(
            '5',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Color(0xFF808080),
            ),
          ),
        ),
        // Space for the continuous vertical line
        const SizedBox(width: 17), // 8px margin + 1px line + 8px margin
        // LOCAL OBJECTIVES text
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              'LOCAL OBJECTIVES',
              style: TextStyle(
                fontSize: ResponsiveFontSizes.bodyLarge(context),
                fontWeight: FontWeight.w700,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLocalObjectiveFieldWithoutNumber() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Empty space to align with line numbers
        Container(width: 20),
        const SizedBox(width: 17),
        // Input field
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFF7F7F7F), width: 0.5),
                borderRadius: BorderRadius.circular(6),
              ),
              alignment: Alignment.center,
              child: TextField(
                controller: _localObjectiveController,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  fillColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                  hintText: 'Type LO name with full step (:)',
                  hintStyle: TextStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  isDense: true,
                ),
                style: TextStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildValidateButton(ObjectCreationProvider provider) {
    return Center(
      child: Container(
        width: double.infinity,
        height: 40,
        margin: const EdgeInsets.only(left: 36),
        child: ElevatedButton(
          onPressed: () {
            _handleValidation(provider);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFF2F2F2),
            elevation: 0,
            shadowColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: const BorderSide(color: Color(0xFFE5E5E5), width: .5),
            ),
          ),
          child: Text(
            'Validate',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.titleLarge(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: const Color(0xFF333333),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSolutionHeader() {
    return Row(
      children: [
        Text(
          'Solution: Customer Onboarding',
          style: TextStyle(
            fontSize: ResponsiveFontSizes.bodyLarge(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildAgentTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Agent Type',
          style: TextStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: const Color(0xFFDDDDDD), width: 1),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedRole,
              hint: Padding(
                padding: const EdgeInsets.only(left: 12),
                child: Text(
                  'Select Type',
                  style: TextStyle(
                    fontSize: ResponsiveFontSizes.titleMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: const Color(0xFF999999),
                  ),
                ),
              ),
              icon: const Padding(
                padding: EdgeInsets.only(right: 12),
                child: Icon(
                  Icons.keyboard_arrow_down,
                  color: Color(0xFF666666),
                ),
              ),
              isExpanded: true,
              items: _roleOptions.map((String role) {
                return DropdownMenuItem<String>(
                  value: role,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 12),
                    child: Text(
                      role,
                      style: TextStyle(
                        fontSize: ResponsiveFontSizes.titleMedium(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedRole = newValue;
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description:',
          style: TextStyle(
            fontSize: ResponsiveFontSizes.bodyLarge(context),
            fontWeight: FontWeight.w700,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          constraints: const BoxConstraints(minHeight: 60),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: const Color(0xFFDDDDDD), width: 1),
          ),
          child: Text(
            _descriptionController.text.isNotEmpty
                ? _descriptionController.text
                : 'Comprehensive customer onboarding process from registration to welcome completion',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLocalObjectivesSection() {
    return Text(
      'Local Objectives',
      style: TextStyle(
        fontSize: ResponsiveFontSizes.bodyLarge(context),
        fontWeight: FontWeight.w700,
        fontFamily: FontManager.fontFamilyTiemposText,
        color: Colors.black,
      ),
    );
  }

  Widget _buildObjectiveExpansionTile(String title, bool isExpanded) {
    return Transform.translate(
      offset: const Offset(0, -8), // Move up to align with line number
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ExpansionTile(
          initiallyExpanded: isExpanded,
          tilePadding: const EdgeInsets.symmetric(vertical: 8),
          childrenPadding: const EdgeInsets.only(top: 4, bottom: 8),
          title: Text(
            title,
            style: TextStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          trailing: Icon(
            isExpanded ? Icons.remove : Icons.add,
            color: const Color(0xFF666666),
            size: 20,
          ),
          children: [
            if (isExpanded) ...[
              Align(
                alignment: Alignment.centerLeft,
                child: GestureDetector(
                  onTap: () {
                    // Handle create pathway
                  },
                  child: Text(
                    'Create pathway',
                    style: TextStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: const Color(0xFF007AFF),
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _handleValidation(ObjectCreationProvider provider) {
    if (_solutionController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter solution name');
      return;
    }

    if (_selectedRole == null) {
      _showErrorSnackBar('Please select a role');
      return;
    }

    if (_descriptionController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter description');
      return;
    }

    if (_localObjectiveController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter local objective');
      return;
    }

    // Show expansion panels after successful validation
    setState(() {
      _showExpansionPanels = true;
    });

    _showSuccessSnackBar('Solution details validated successfully!');
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showInfoPopup(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Drag handle
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 24),

                // Message text
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 40),
                  child: Text(
                    'This Objects is already exists in your library. You need to rename the objects to proceed.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                      height: 1.4,
                    ),
                  ),
                ),
                const SizedBox(height: 36),

                // Buttons row
                Row(
                  children: [
                    // Continue button (left)
                    Expanded(
                      child: Container(
                        height: 40,
                        child: OutlinedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: OutlinedButton.styleFrom(
                            backgroundColor: Colors.white,
                            side: const BorderSide(
                              color: Color(0xFF797676),
                              width: .5,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          child: Text(
                            'Cancel',
                            style: TextStyle(
                              fontSize: ResponsiveFontSizes.titleLarge(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: const Color(0xFF333333),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Continue button (right - blue)
                    Expanded(
                      child: Container(
                        height: 40,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF007AFF),
                            elevation: 0,
                            shadowColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          child: Text(
                            'Continue',
                            style: TextStyle(
                              fontSize: ResponsiveFontSizes.titleLarge(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }
}
