class EmailModel {
  EntityAttributeNames? functionType;
  EntityAttributeNames? functionName;
  EntityAttributeNames? senderEmailAddress;
  EntityAttributeNames? receipientAddress;
  EntityAttributeNames? emailSubjectLine;
  EntityAttributeNames? emailMessageBody;
  EntityAttributeNames? attachments;
  EntityAttributeNames? ccRecipients;
  EntityAttributeNames? bccRecipients;

  // 🆕 Dropdown selections
  String? source;


  EmailModel({
    this.functionType,
    this.functionName,
    this.senderEmailAddress,
    this.receipientAddress,
    this.emailSubjectLine,
    this.emailMessageBody,
    this.attachments,
    this.ccRecipients,
    this.bccRecipients,
    this.source,
  });

  EmailModel copyWith({
    EntityAttributeNames? functionType,
    EntityAttributeNames? functionName,
    EntityAttributeNames? senderEmailAddress,
    EntityAttributeNames? receipientAddress,
    EntityAttributeNames? emailSubjectLine,
    EntityAttributeNames? emailMessageBody,
    EntityAttributeNames? attachments,
    EntityAttributeNames? ccRecipients,
    EntityAttributeNames? bccRecipients,
    String? source,
    EntityAttributeNames? popupDropdownSelection,
    List<String>? nestedDropdownValues,
  }) =>
      EmailModel(
        functionType: functionType ?? this.functionType,
        functionName: functionName ?? this.functionName,
        senderEmailAddress: senderEmailAddress ?? this.senderEmailAddress,
        receipientAddress: receipientAddress ?? this.receipientAddress,
        emailSubjectLine: emailSubjectLine ?? this.emailSubjectLine,
        emailMessageBody: emailMessageBody ?? this.emailMessageBody,
        attachments: attachments ?? this.attachments,
        ccRecipients: ccRecipients ?? this.ccRecipients,
        bccRecipients: bccRecipients ?? this.bccRecipients,
        source:
            source ?? this.source,
     
      );

  factory EmailModel.fromJson(Map<String, dynamic> json) => EmailModel(
        functionType: json["functionType"],
        functionName: json["functionName"],
        senderEmailAddress: json["senderEmailAddress"],
        receipientAddress: json["receipientAddress"],
        emailSubjectLine: json["emailSubjectLine"],
        emailMessageBody: json["emailMessageBody"],
        attachments: json["attachments"],
        ccRecipients: json["ccRecipients"],
        bccRecipients: json["bccRecipients"],
        source: json["source"],
      );

  Map<String, dynamic> toJson() => {
        "functionType": functionType,
        "functionName": functionName,
        "senderEmailAddress": senderEmailAddress,
        "receipientAddress": receipientAddress,
        "emailSubjectLine": emailSubjectLine,
        "emailMessageBody": emailMessageBody,
        "attachments": attachments,
        "ccRecipients": ccRecipients,
        "bccRecipients": bccRecipients,
        "source": source,
      };
}

class EntityAttributeNames{
  String? entityName;
  String? attributeName;

  EntityAttributeNames({
    this.entityName,
    this.attributeName,
  });

  EntityAttributeNames copyWith({
    String? entityName,
    String? attributeName,
  }) =>
      EntityAttributeNames(
        entityName: entityName ?? this.entityName,
        attributeName: attributeName ?? this.attributeName,
      );

  factory EntityAttributeNames.fromJson(Map<String, dynamic> json) => EntityAttributeNames(
        entityName: json["entityName"],
        attributeName: json["attributeName"],
      );

  Map<String, dynamic> toJson() => {
        "entityName": entityName,
        "attributeName": attributeName,
      };

}
