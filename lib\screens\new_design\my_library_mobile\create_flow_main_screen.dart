import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_develop_screen.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_discover_screen.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_project_details_form_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/recent_items_screen.dart';
import 'package:nsl/screens/new_design/my_library_mobile/favourite_items_screen.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_object_screen_static_mobile.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:provider/provider.dart';

class MobileCreationFlowMainScreen extends StatefulWidget {
  const MobileCreationFlowMainScreen({Key? key}) : super(key: key);

  @override
  State<MobileCreationFlowMainScreen> createState() =>
      _MobileCreationFlowMainScreenState();
}

class _MobileCreationFlowMainScreenState
    extends State<MobileCreationFlowMainScreen> {
  String _selectedTab = 'Recent';
  int _currentPage = 0;
  final int _itemsPerPage = 10;

  // Category filter functionality
  String? _selectedCategoryFilter;

  // Hover state tracking for table rows
  int? _hoveredRowIndex;

  // Static data for the table
  List<Map<String, dynamic>> _allData = [
    // Role entries
    {
      'fileName': 'CEO',
      'type': 'Role',
      'project': '',
      'solution': '',
      'object': '',
      'role': 'Role',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': true,
      'status': 'Draft',
    },
    {
      'fileName': 'Product Manager',
      'type': 'Role',
      'project': '',
      'solution': '',
      'object': '',
      'role': 'Role',
      'lastOpened': DateTime(2025, 9, 23),
      'isFavorite': false,
      'status': 'Published',
    },
    {
      'fileName': 'Admin',
      'type': 'Role',
      'project': '',
      'solution': '',
      'object': '',
      'role': 'Role',
      'lastOpened': DateTime(2025, 9, 21),
      'isFavorite': false,
      'status': 'Draft',
    },

    // Object entries
    {
      'fileName': 'Employee',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': false,
      'status': 'Published',
    },
    {
      'fileName': 'Order',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 22),
      'isFavorite': false,
      'status': 'Draft',
    },
    {
      'fileName': 'Customer',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 21),
      'isFavorite': true,
      'status': 'Published',
    },
    {
      'fileName': 'Product',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 20),
      'isFavorite': true,
      'status': 'Draft',
    },

    // Solution entries
    {
      'fileName': 'Customer order product',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': false,
      'status': 'Published',
    },
    {
      'fileName': 'Inventory System',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 23),
      'isFavorite': true,
      'status': 'Draft',
    },
    {
      'fileName': 'Sales Dashboard',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 22),
      'isFavorite': false,
      'status': 'Draft',
    },
    {
      'fileName': 'Payment System',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 20),
      'isFavorite': false,
      'status': 'Published',
    },

    // Project entries
    {
      'fileName': 'E-commerce Platform',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 25),
      'isFavorite': true,
      'status': 'Draft',
    },
    {
      'fileName': 'CRM System',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': false,
      'status': 'Published',
    },
    {
      'fileName': 'Mobile App Development',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 23),
      'isFavorite': true,
      'status': 'Draft',
    },
    {
      'fileName': 'Data Analytics Dashboard',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 22),
      'isFavorite': false,
      'status': 'Published',
    },
  ];
  List<Map<String, dynamic>> get _filteredData {
    List<Map<String, dynamic>> filtered = List.from(_allData);

    // Apply category filter only (remove tab filtering)
    if (_selectedCategoryFilter != null) {
      filtered = filtered
          .where((item) => item['type'] == _selectedCategoryFilter)
          .toList();
    }

    // Sort the data by last opened (most recent first) - always show recent
    filtered.sort((a, b) {
      DateTime aDate = a['lastOpened'];
      DateTime bDate = b['lastOpened'];
      return bDate.compareTo(aDate);
    });

    return filtered;
  }

  List<Map<String, dynamic>> get _paginatedData {
    final startIndex = _currentPage * _itemsPerPage;
    final endIndex =
        (startIndex + _itemsPerPage).clamp(0, _filteredData.length);
    return _filteredData.sublist(startIndex, endIndex);
  }

  int get _totalPages => (_filteredData.length / _itemsPerPage).ceil();

  void _showDiscoverModal() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DiscoverModalScreen(),
      ),
    );
  }

  void _showDevelopModal() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DevelopModalScreen(),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  // Show sort bottom sheet
  void _showSortBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
          ),
          child: Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 20), // Uniform padding
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.only(top: 20, bottom: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Sort by',
                        style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Icon(Icons.close, color: Colors.black, size: 24),
                      ),
                    ],
                  ),
                ),

                Divider(height: 1, color: Colors.grey.shade300),

                _buildSortOption('Projects', 'project'),
                Divider(height: 1, color: Colors.grey.shade300),
                _buildSortOption('Solutions', 'solution'),
                Divider(height: 1, color: Colors.grey.shade300),
                _buildSortOption('Objects', 'object'),
                Divider(height: 1, color: Colors.grey.shade300),
                _buildSortOption('Roles/Agent', 'role'),
                const SizedBox(height: 20),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSortOption(String title, String column) {
    final category = _getCategoryFromColumn(column);

    String svgPath;
    switch (column) {
      case 'project':
        svgPath = 'assets/images/my_library/create_project_mobile.svg';
        break;
      case 'solution':
        svgPath = 'assets/images/my_library/create_solution_mobile.svg';
        break;
      case 'object':
        svgPath = 'assets/images/my_library/create_object_mobile.svg';
        break;
      case 'role':
        svgPath = 'assets/images/my_library/create_roles_mobile.svg';
        break;
      default:
        svgPath = 'assets/images/my_library/my_library_discover.svg';
    }
    return GestureDetector(
      onTap: () {
        _filterByCategory(column);
        Navigator.pop(context);
      },
      child: Padding(
        padding:
            const EdgeInsets.symmetric(vertical: 12), // Only vertical padding
        child: Row(
          children: [
            SvgPicture.asset(
              svgPath,
              width: 14,
              height: 14,
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      drawer: CustomDrawer(),
      // Fixed AppBar that never scrolls away
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(Icons.menu),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
          ),
        ),
        title: Text(''),
        backgroundColor: Color(0xffF7F9FB),
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      body: CustomScrollView(
        slivers: [
          // Scrollable top section that disappears completely
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
              // padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: (16.0),
              child: _buildTopSection(),
            ),
          ),

          // Sticky tab bar that pins after top section scrolls away
          SliverPersistentHeader(
            pinned: true,
            delegate: _StickyHeaderDelegate(
              child: Container(
                color: Color(0xffF7F9FB),
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  children: [
                    _buildTabBar(),
                    const SizedBox(height: 8),
                  ],
                ),
              ),
            ),
          ),

          // Scrollable table content below the pinned tab bar
          SliverFillRemaining(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          _buildTable(),
                          const SizedBox(height: 16),
                          _buildPagination(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // More templates link at top right
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () {
                  // Handle more templates tap
                },
                child: Text(
                  'More templates',
                  style: TextStyle(
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textBlue,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Discover and Develop sections in column
        Column(
          children: [
            _buildDiscoverSection(),
            const SizedBox(height: 16),
            _buildDevelopSection(),
          ],
        ),
      ],
    );
  }

  Widget _buildDiscoverSection() {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          // Clear chat data for fresh start when navigating from Discover section
          final webHomeProviderStatic =
              Provider.of<WebHomeProviderStatic>(context, listen: false);
          final webHomeProvider =
              Provider.of<WebHomeProvider>(context, listen: false);

          // Reset both providers to ensure complete data clearing
          webHomeProviderStatic.resetConversation(preserveScreenIndex: true);
          webHomeProvider.resetConversation(preserveScreenIndex: true);

          // Clear any additional chat-related data
          webHomeProviderStatic.clearMessages();
          webHomeProvider.clearMessages();

          // Clear chat controllers
          webHomeProviderStatic.chatController.clear();
          webHomeProvider.chatController.clear();

          // Reset UI state completely
          webHomeProviderStatic.resetUIState(preserveScreenIndex: true);
          webHomeProvider.resetUIState(preserveScreenIndex: true);

          // Clear any NSL session data
          webHomeProvider.resetNslSession();
          webHomeProvider.resetModeSession();

          // Clear any solution session data
          webHomeProviderStatic.solutionSessionModel = null;
          webHomeProvider.solutionSessionModel = null;

          // Force clear any cached data
          webHomeProviderStatic.lastUserMessageForApi = '';
          webHomeProvider.lastUserMessageForApi = '';

          // Navigate to the screen
          webHomeProvider.currentScreenIndex = ScreenConstants.create;
          webHomeProviderStatic.currentCreateScreenIndex = 1;
          // Navigator.push(
          //   context,
          //   MaterialPageRoute(
          //     builder: (context) => CreateProjectDetailsFormMobile(
          //       onComplete: (p0, p1, p2, p3) {},
          //       onBack: () {},
          //     ),
          //   ),
          // );

          Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  CreateProjectDetailsFormMobile(
                onComplete: (p0, p1, p2, p3) {},
                onBack: () {},
              ),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                const begin = Offset(-1.0, 0.0); // Slide from right
                const end = Offset.zero;
                const curve = Curves.easeInOut;
                final tween = Tween(begin: begin, end: end)
                    .chain(CurveTween(curve: curve));
                return SlideTransition(
                  position: animation.drive(tween),
                  child: child,
                );
              },
              transitionDuration: const Duration(milliseconds: 600),
            ),
          );
// or use Navigator.pushNamed(context, '/create'); if you want to push on top of the stack
        },
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color: Colors.white,
            // border: Border.all(color: AppColors.greyBorder),
            borderRadius: BorderRadius.circular(AppSpacing.xxs),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  SvgPicture.asset(
                    'assets/images/my_library/my_library_discover.svg', // your SVG asset path
                    width: 20,
                    height: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Discover',
                    style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        color: Colors.black),
                  ),
                  const Spacer(),
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: _showDiscoverModal,
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                              color: AppColors.textGreyColor, width: 1),
                        ),
                        child: Center(
                          child: Text(
                            'i',
                            style: TextStyle(
                              fontFamily: FontManager.fontFamilyTiemposText,
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              color: AppColors.textGreyColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Share your industry and the solutions you need—our NSL AI will handle complete solution discovery and build it tailored to your needs.',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textGreyColor,
                  // height: 2,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDevelopSection() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateObjectScreenStaticMobile(),
            ),
          );
        },
        // hoverColor: Colors.grey.withOpacity(0.1),
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppSpacing.xxs),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  SvgPicture.asset(
                    'assets/images/my_library/my_library_develop.svg',
                    width: 20,
                    height: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Develop',
                    style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        color: Colors.black),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: _showDevelopModal,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                            color: AppColors.textGreyColor, width: 1),
                      ),
                      child: Center(
                        child: Text(
                          'i',
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textGreyColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Enter or upload your requirement, and we\'ll extract, develop, and refine your solution with AI-guided suggestions throughout.',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  color: AppColors.textGreyColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Widget _buildDevelopSection() {
  //   return GestureDetector(
  //     onTap: () {
  //     print('Develop section tapped'); // Add this for debugging

  //     // Use Future.microtask instead of addPostFrameCallback
  //     Future.microtask(() {
  //       if (mounted) {
  //         Provider.of<WebHomeProvider>(context, listen: false)
  //             .currentScreenIndex = ScreenConstants.createObjectScreenStatic;
  //       }
  //     });
  //   },
  //     child: MouseRegion(
  //        cursor: SystemMouseCursors.click,
  //       child: Container(
  //         padding: const EdgeInsets.all(AppSpacing.lg),
  //         decoration: BoxDecoration(
  //           color: Colors.white,
  //           // border: Border.all(color: AppColors.greyBorder),
  //           borderRadius: BorderRadius.circular(AppSpacing.xxs),
  //         ),
  //         child: Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             Row(
  //               children: [
  //                 SvgPicture.asset(
  //                   'assets/images/my_library/my_library_develop.svg', // your SVG asset path
  //                   width: 20,
  //                   height: 20,
  //                 ),
  //                 const SizedBox(width: 8),
  //                 Text(
  //                   'Develop',
  //                   style: TextStyle(
  //                       fontFamily: FontManager.fontFamilyTiemposText,
  //                       fontSize: 16,
  //                       fontWeight: FontWeight.w400,
  //                       color: Colors.black),
  //                 ),
  //                 const Spacer(),
  //                 MouseRegion(
  //                   cursor: SystemMouseCursors.click,
  //                   child: GestureDetector(
  //                     onTap: _showDevelopModal,
  //                     child: Container(
  //                       width: 16,
  //                       height: 16,
  //                       decoration: BoxDecoration(
  //                         shape: BoxShape.circle,
  //                         border:
  //                             Border.all(color: AppColors.textGreyColor, width: 1),
  //                       ),
  //                       child: Center(
  //                         child: Text(
  //                           'i',
  //                           style: TextStyle(
  //                             fontFamily: FontManager.fontFamilyTiemposText,
  //                             fontSize: 10,
  //                             fontWeight: FontWeight.w500,
  //                             color: AppColors.textGreyColor,
  //                           ),
  //                         ),
  //                       ),
  //                     ),
  //                   ),
  //                 ),
  //               ],
  //             ),
  //             const SizedBox(height: 12),
  //             Text(
  //               'Enter or upload your requirement, and we\'ll extract, develop, and refine your solution with AI-guided suggestions throughout.',
  //               style: TextStyle(
  //                 fontFamily: FontManager.fontFamilyTiemposText,
  //                 fontSize: 10,
  //                 fontWeight: FontWeight.w400,
  //                 color: AppColors.textGreyColor,
  //                 // height: 2,
  //               ),
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildTabBar() {
    final tabs = ['Recent', 'Favourite', 'Chat'];
    final tabIcons = {
      'Recent': 'assets/images/my_library/create_recent_mobile.svg',
      'Favourite': 'assets/images/my_library/create__fav.svg',
      'Chat': 'assets/images/my_library/create_chat.svg',
    };

    return Row(
      children: [
        ...tabs.map(
          (tab) => _buildTabItem(tab, tabIcons[tab]!),
        ),
        Spacer(),
        _buildSortButton(),
      ],
    );
  }

  Widget _buildTabItem(String tab, String iconPath) {
    return GestureDetector(
      onTap: () {
        if (tab == 'Recent') {
          Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  RecentItemsScreen(allData: _allData),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                const begin = Offset(-1.0, 0.0); // Slide from right
                const end = Offset.zero;
                const curve = Curves.easeInOut;
                final tween = Tween(begin: begin, end: end)
                    .chain(CurveTween(curve: curve));
                return SlideTransition(
                  position: animation.drive(tween),
                  child: child,
                );
              },
              transitionDuration: const Duration(milliseconds: 600),
            ),
          );
        } else if (tab == 'Favourite') {
          Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) =>
                  FavouriteItemsScreen(allData: _allData),
              transitionsBuilder:
                  (context, animation, secondaryAnimation, child) {
                const begin = Offset(-1.0, 0.0); // Slide from right
                const end = Offset.zero;
                const curve = Curves.easeInOut;
                final tween = Tween(begin: begin, end: end)
                    .chain(CurveTween(curve: curve));
                return SlideTransition(
                  position: animation.drive(tween),
                  child: child,
                );
              },
              transitionDuration: const Duration(milliseconds: 600),
            ),
          );
        } else if (tab == 'Chat') {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => FavouriteItemsScreen(allData: _allData),
              ));
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            color: Color(0xffE0E0E0),
            width: .5,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              iconPath,
              width: 16,
              height: 16,
            ),
            const SizedBox(width: 6),
            Text(
              tab,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Color(0xff6B7280),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSortButton() {
    return GestureDetector(
      onTap: _showSortBottomSheet,
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.grey, // Change color as needed
                width: .5, // Change width as needed
              ),
              borderRadius:
                  BorderRadius.circular(0), // Optional: rounded corners
            ),
            child: Row(
              children: [
                SvgPicture.asset(
                  'assets/images/my_library/create_sort.svg',
                  width: 20,
                  height: 20,
                ),
              ],
            )),
      ),
    );
  }

  Widget _buildTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        // border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // _buildTableHeader(),
          ListView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: _paginatedData.length,
            itemBuilder: (context, index) {
              return _buildTableRow(_paginatedData[index], index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xffD0D0D0), width: 0.5),
          top: BorderSide(color: Color(0xffD0D0D0), width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // File Name - Left aligned
          Expanded(
            flex: 4,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'File Name',
                style: TextStyle(
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.black),
              ),
            ),
          ),
          // Last Opened
          Expanded(
            flex: 3,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Last Opened',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          // Favorites column - empty header for star icon space
          Expanded(
            flex: 1,
            child: Container(),
          ),
        ],
      ),
    );
  }

  String? _getCategoryFromColumn(String column) {
    switch (column) {
      case 'project':
        return 'Project';
      case 'solution':
        return 'Solution';
      case 'object':
        return 'Object';
      case 'role':
        return 'Role';
      default:
        return null;
    }
  }

  void _filterByCategory(String column) {
    final category = _getCategoryFromColumn(column);
    setState(() {
      if (_selectedCategoryFilter == category) {
        // If already selected, clear the filter
        _selectedCategoryFilter = null;
      } else {
        // Set new filter
        _selectedCategoryFilter = category;
      }
      _currentPage = 0; // Reset to first page when filtering
    });
  }

  Widget _buildTableRow(Map<String, dynamic> item, int index) {
    final isEvenRow = index % 2 == 0;
    final isHovered = _hoveredRowIndex == index;
    final isFavorite = item['isFavorite'] ?? false;

    return MouseRegion(
      onEnter: (_) => setState(() => _hoveredRowIndex = index),
      onExit: (_) => setState(() => _hoveredRowIndex = null),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isEvenRow ? Colors.white : Colors.white,
          border: Border(
            bottom: BorderSide(color: Color(0xffE6EAEE), width: 0.5),
          ),
        ),
        child: Row(
          children: [
            // Left icon section
            _buildTypeIcon(item['type']),
            const SizedBox(width: 12),

            // Middle section - File name and type with date
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['fileName'],
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimaryLight,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Row(
                    children: [
                      if (item['type'].isNotEmpty)
                        Text(
                          item['type'],
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 10,
                            fontWeight: FontWeight.w400,
                            color: AppColors.black,
                          ),
                        ),
                      if (item['type'].isNotEmpty)
                        Text(
                          ' | ',
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 10,
                            fontWeight: FontWeight.w400,
                            color: AppColors.textGreyColor,
                          ),
                        ),
                      Text(
                        _formatDate(item['lastOpened']),
                        style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: 10,
                          fontWeight: FontWeight.w400,
                          color: AppColors.textGreyColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Right section - Only star icon
            GestureDetector(
              onTap: () {
                setState(() {
                  item['isFavorite'] = !item['isFavorite'];
                });
              },
              child: Container(
                padding: const EdgeInsets.all(8.0), // Larger tap area
                child: Icon(
                  item['isFavorite'] ? Icons.star : Icons.star_border,
                  size: 24, // Slightly larger for mobile
                  color:
                      item['isFavorite'] ? Colors.amber : Colors.grey.shade400,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeIcon(String type) {
    String svgAsset;

    switch (type) {
      case 'Role':
        svgAsset = 'assets/images/my_library/create_role.svg';
        break;
      case 'Object':
        svgAsset = 'assets/images/my_library/create_object.svg';
        break;
      case 'Solution':
        svgAsset = 'assets/images/my_library/create_solution.svg';
        break;
      case 'Project':
        svgAsset = 'assets/images/my_library/create_project.svg';
        break;
      default:
        svgAsset = 'assets/images/my_library/default.svg';
    }

    return Center(
      child: CustomImage.asset(
        svgAsset,
        width: 32,
        height: 32,
        fit: BoxFit.contain,
      ).toWidget(),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday, ${date.day}/${date.month}/${date.year}';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Widget _buildPagination() {
    if (_totalPages <= 1) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppSpacing.md),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Navigation buttons
          Row(
            children: [
              // Previous button
              _HoverPaginationButton(
                icon: const Icon(Icons.chevron_left, size: 20),
                onPressed: _currentPage > 0
                    ? () {
                        setState(() {
                          _currentPage--;
                        });
                      }
                    : null,
              ),
              const SizedBox(width: 8),
              // Next button
              _HoverPaginationButton(
                icon: const Icon(Icons.chevron_right, size: 20),
                onPressed: _currentPage < _totalPages - 1
                    ? () {
                        setState(() {
                          _currentPage++;
                        });
                      }
                    : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModal({required String title, required Widget content}) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 500),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: AppColors.greyBorder, width: 1),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimaryLight,
                      ),
                    ),
                  ),
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        size: 24,
                        color: AppColors.textGreyColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: content,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscoverModalContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildModalStep(
          'Step 1: Describe Your solution',
          'Specify your business industry, organization size, operational requirements, and geographic locations for the solution.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 2: AI Analysis',
          'Our AI engine automatically discovers and lists out the roles, entities, and workflows for your solution',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 3: Review & Customize',
          'Validate the document in details and customize any components you want to modify with prompt',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 4: Development',
          'Once you have confirmed the final components, proceed with the development of your solution using the discovered framework',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 5: Testing',
          'Test your completed solution to ensure it works as expected.',
        ),
      ],
    );
  }

  Widget _buildModalStep(String title, String description) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimaryLight,
          ),
        ),
        if (description.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontFamily: FontManager.fontFamilyTiemposText,
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: AppColors.textGreyColor,
              height: 1.4,
            ),
          ),
        ],
      ],
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.onPressed == null
                ? Colors.grey.shade200
                : (isHovered ? Color(0xff0058FF) : Colors.grey.shade300),
            width: 1.0,
          ),
          // No border radius when hovered
          borderRadius: isHovered ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: widget.onPressed == null
              ? Colors.grey.shade400
              : (isHovered ? Color(0xff0058FF) : Colors.black),
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}

class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _StickyHeaderDelegate({required this.child});

  @override
  double get minExtent => 60.0; // Height for tabs only

  @override
  double get maxExtent => 60.0; // Height for tabs only

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
