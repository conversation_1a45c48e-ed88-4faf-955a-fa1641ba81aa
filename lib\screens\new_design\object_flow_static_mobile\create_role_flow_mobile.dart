import 'package:flutter/material.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:provider/provider.dart';

class CreateRoleFlowMobile extends StatefulWidget {
  const CreateRoleFlowMobile({super.key});

  @override
  State<CreateRoleFlowMobile> createState() => _CreateRoleFlowMobileState();
}

class _CreateRoleFlowMobileState extends State<CreateRoleFlowMobile> {
  final TextEditingController _roleController = TextEditingController();
  final TextEditingController _reportsToController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  String? _selectedIcon;
  bool _showExpansionPanels = false;

  @override
  void dispose() {
    _roleController.dispose();
    _reportsToController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ObjectCreationProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: SafeArea(
            child: Column(
              children: [
                // Header
                _buildHeader(context),

                // Content area
                Expanded(
                  child: _buildContent(context, provider),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 24,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 8),
          // Title
          Expanded(
            child: Text(
              'Create Role Flow',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.white,
                fontWeight: FontWeight.bold,
                height: 1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, ObjectCreationProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
          physics: const AlwaysScrollableScrollPhysics(),
        ),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - 200,
            ),
            child: _buildContentWithLineNumbers(context, provider),
          ),
        ),
      ),
    );
  }

  Widget _buildContentWithLineNumbers(
      BuildContext context, ObjectCreationProvider provider) {
    int lineNumber = 1;
    final List<Widget> allWidgets = [];

    if (!_showExpansionPanels) {
      // Line 1: Role: label
      allWidgets.add(_buildLineWithNumber(lineNumber++, _buildRoleLabel()));

      // Line 2: Role input field
      allWidgets.add(_buildLineWithNumber(lineNumber++, _buildRoleField()));

      // Line 3: Reports To field
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, _buildReportsToField()));

      // Line 4: Validate button
      allWidgets.add(
          _buildLineWithNumber(lineNumber++, _buildValidateButtonWithLine()));

      // Line 5: Description field
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, _buildDescriptionField()));
    } else {
      // Show expansion panels after validation
      allWidgets.add(_buildLineWithNumber(lineNumber++, _buildRoleHeader()));
      allWidgets.add(const SizedBox(height: 8));
      // Add any additional content after validation here
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: allWidgets,
    );
  }

  Widget _buildLineWithNumber(int lineNumber, Widget content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Line number
        Container(
          width: 20,
          child: Text(
            '$lineNumber',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Color(0xFF808080),
            ),
          ),
        ),
        // Space for the continuous vertical line
        const SizedBox(width: 16), // 8px margin + 1px line + 8px margin
        // Content
        Expanded(child: content),
      ],
    );
  }

  Widget _buildRoleLabel() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Text(
        'Role:',
        style: TextStyle(
          fontSize: ResponsiveFontSizes.bodyLarge(context),
          fontWeight: FontWeight.bold,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
      ),
    );
  }

  Widget _buildRoleField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Role',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 40,
            decoration: BoxDecoration(
              border: Border.all(
                color: const Color(0xFF7F7F7F),
                width: .5,
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            alignment: Alignment.centerLeft,
            child: TextField(
              controller: _roleController,
              decoration: InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                isDense: true,
                hintText: '',
                hintStyle: TextStyle(
                  fontSize: ResponsiveFontSizes.titleLarge(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              style: TextStyle(
                fontSize: ResponsiveFontSizes.titleLarge(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportsToField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Reports to Board Of Director',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 40,
            decoration: BoxDecoration(
              border: Border.all(
                color: const Color(0xFF7F7F7F),
                width: .5,
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            alignment: Alignment.centerLeft,
            child: TextField(
              controller: _reportsToController,
              decoration: InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                isDense: true,
                hintText: '',
                hintStyle: TextStyle(
                  fontSize: ResponsiveFontSizes.titleLarge(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              style: TextStyle(
                fontSize: ResponsiveFontSizes.titleLarge(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidateButtonWithLine() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Center(
        child: Container(
          width: double.infinity,
          height: 40,
          child: ElevatedButton(
            onPressed: () {
              _handleValidation(context.read<ObjectCreationProvider>());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFF2F2F2),
              elevation: 0, // ✅ No elevation by default
              shadowColor: Colors.transparent, // ✅ No shadow even when pressed

              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
                side: const BorderSide(
                  color: Color(0xFFE5E5E5),
                  width: .5,
                ),
              ),
            ),
            child: Text(
              'Validate',
              style: TextStyle(
                fontSize: ResponsiveFontSizes.titleLarge(context),
                fontWeight: FontWeight.w500,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: const Color(0xFF333333),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReportsToAndIconRow() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          // Reports To section
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Reports To',
                  style: TextStyle(
                    fontSize: ResponsiveFontSizes.titleSmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  height: 40,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: const Color(0xFF7F7F7F),
                      width: 0.5,
                    ),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  alignment: Alignment.center,
                  child: TextField(
                    controller: _reportsToController,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      fillColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      contentPadding:
                          const EdgeInsets.symmetric(horizontal: 12),
                      isDense: true,
                      hintText: 'Board of Directors',
                      hintStyle: TextStyle(
                        fontSize: ResponsiveFontSizes.titleMedium(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.grey.shade400,
                      ),
                    ),
                    style: TextStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          // Icon section
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Icon (64x64 Pixel):',
                  style: TextStyle(
                    fontSize: ResponsiveFontSizes.titleSmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  height: 40,
                  child: ElevatedButton(
                    onPressed: () {
                      // Handle browse functionality
                      _showIconSelector(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                        side: const BorderSide(
                          color: Color(0xFF7F7F7F),
                          width: 0.5,
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Browse',
                          style: TextStyle(
                            fontSize: ResponsiveFontSizes.titleMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Description:',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 51,
            decoration: BoxDecoration(
              border: Border.all(
                color: Color(0xFF7F7F7F),
                width: .5,
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Stack(
              children: [
                TextField(
                  controller: _descriptionController,
                  maxLines: null,
                  expands: true,
                  textAlignVertical: TextAlignVertical.top,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    fillColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    contentPadding: EdgeInsets.fromLTRB(12, 12, 40, 12),
                  ),
                  style: TextStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF2F2F2),
                      borderRadius: BorderRadius.circular(24),
                      border: Border.all(
                        color: const Color(0xFF7F7F7F),
                        width: .5,
                      ),
                    ),
                    child: const Icon(
                      Icons.add,
                      size: 16,
                      color: Color(0xFF666666),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidateButton(ObjectCreationProvider provider) {
    return Center(
      child: Container(
        width: double.infinity,
        height: 40,
        margin: const EdgeInsets.only(left: 36),
        child: ElevatedButton(
          onPressed: () {
            _handleValidation(provider);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFF2F2F2),
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: const BorderSide(
                color: Color(0xFFE5E5E5),
                width: .5,
              ),
            ),
          ),
          child: Text(
            'Validate',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.titleLarge(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: const Color(0xFF333333),
            ),
          ),
        ),
      ),
    );
  }

  void _showIconSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Select Icon',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: GridView.count(
                  crossAxisCount: 4,
                  children: [
                    _buildIconOption(Icons.person, 'Person'),
                    _buildIconOption(Icons.work, 'Work'),
                    _buildIconOption(Icons.group, 'Team'),
                    _buildIconOption(Icons.star, 'Star'),
                    _buildIconOption(Icons.account_balance, 'Corporate'),
                    _buildIconOption(Icons.supervisor_account, 'Supervisor'),
                    _buildIconOption(Icons.business_center, 'Business'),
                    _buildIconOption(Icons.badge, 'Badge'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildIconOption(IconData icon, String name) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedIcon = name;
        });
        Navigator.pop(context);
      },
      child: Container(
        margin: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 24, color: Colors.grey.shade700),
            const SizedBox(height: 4),
            Text(
              name,
              style: TextStyle(
                fontSize: 10,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleHeader() {
    return Row(
      children: [
        Text(
          'Role: ',
          style: TextStyle(
            fontSize: ResponsiveFontSizes.bodyLarge(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        Text(
          _roleController.text.isNotEmpty ? _roleController.text : 'Role Flow',
          style: TextStyle(
            fontSize: ResponsiveFontSizes.bodyLarge(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: const Color(0xFF007AFF),
          ),
        ),
      ],
    );
  }

  void _handleValidation(ObjectCreationProvider provider) {
    if (_roleController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter role name');
      return;
    }

    if (_reportsToController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter who this role reports to');
      return;
    }

    if (_descriptionController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter description');
      return;
    }

    // Show expansion panels after successful validation
    setState(() {
      _showExpansionPanels = true;
    });

    _showSuccessSnackBar('Role details validated successfully!');

    // Navigate to next screen if needed
    // Navigator.of(context).push(
    //   MaterialPageRoute(
    //     builder: (context) => const NextScreen(),
    //   ),
    // );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
