import 'package:flutter/material.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/screens/new_design/object_flow_static_mobile/create_solution_onboarding_details_mobile.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:provider/provider.dart';

class CreateGoFlowDetailsMobile extends StatefulWidget {
  const CreateGoFlowDetailsMobile({super.key});

  @override
  State<CreateGoFlowDetailsMobile> createState() =>
      _CreateGoFlowDetailsMobileState();
}

class _CreateGoFlowDetailsMobileState extends State<CreateGoFlowDetailsMobile> {
  final TextEditingController _objectController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController(
    text:
        'Comprehensive customer onboarding process from registration to welcome completion',
  );
  String? _selectedType;
  String? _selectedIcon;
  bool _showExpansionPanels = false;

  final List<String> _typeOptions = [
    'Agent Type 1',
    'Agent Type 2',
    'Agent Type 3',
    'Agent Type 4',
    'Agent Type 5',
  ];

  @override
  void dispose() {
    _objectController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ObjectCreationProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: SafeArea(
            child: Column(
              children: [
                // Header
                _buildHeader(context),

                // Content area
                Expanded(
                  child: _buildContent(context, provider),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 24,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 8),
          // Title
          Expanded(
            child: Text(
              'Create Go Flow Details',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.white,
                fontWeight: FontWeight.bold,
                height: 1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, ObjectCreationProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
          physics: const AlwaysScrollableScrollPhysics(),
        ),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - 200,
            ),
            child: _buildContentWithLineNumbers(context, provider),
          ),
        ),
      ),
    );
  }

  Widget _buildContentWithLineNumbers(
      BuildContext context, ObjectCreationProvider provider) {
    int lineNumber = 1;
    final List<Widget> allWidgets = [];

    if (!_showExpansionPanels) {
      // Line 1: Object Detail header
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, _buildObjectDetailHeader()));

      // Line 2: Object input field
      allWidgets.add(_buildLineWithNumber(lineNumber++, _buildObjectField()));

      // Line 3: Select Type and Icon row
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, _buildTypeAndIconRow()));

      // Line 4: Description field
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, _buildDescriptionField()));

      // Validate button (without line number)
      allWidgets.add(_buildValidateButton(provider));
    } else {
      // Show expansion panels after validation
      allWidgets.add(_buildLineWithNumber(lineNumber++, _buildObjectHeader()));
      allWidgets.add(const SizedBox(height: 8));
      // Add any additional content after validation here
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: allWidgets,
    );
  }

  Widget _buildLineWithNumber(int lineNumber, Widget content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Line number
        Container(
          width: 20,
          child: Text(
            '$lineNumber',
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: Color(0xFF808080),
              fontFamily: FontManager.fontFamilyInter,
            ),
          ),
        ),
        // Space for the continuous vertical line
        const SizedBox(width: 17), // 8px margin + 1px line + 8px margin
        // Content
        Expanded(child: content),
      ],
    );
  }

  Widget _buildObjectDetailHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Extracted Details',
              style: TextStyle(
                fontSize: ResponsiveFontSizes.bodyLarge(context),
                fontWeight: FontWeight.w700,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
            const SizedBox(width: 8),
            GestureDetector(
              onTap: () {
                _showInfoPopup(context);
              },
              child: Container(
                width: 16,
                height: 16,
                child: Icon(
                  Icons.info_outline,
                  size: 16,
                  color: Colors.black,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12), // Bottom spacing
      ],
    );
  }

  Widget _buildObjectField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Solution',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 40,
            decoration: BoxDecoration(
              border: Border.all(
                color: const Color(0xFF7F7F7F),
                width: 0.5,
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            alignment: Alignment.center,
            child: TextField(
              controller: _objectController,
              decoration: const InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding: EdgeInsets.symmetric(horizontal: 12),
                isDense: true,
              ),
              style: TextStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeAndIconRow() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          // Select Type section
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Agent Type',
                  style: TextStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  height: 40,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: const Color(0xFF7F7F7F),
                      width: 0.5,
                    ),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _selectedType,
                      hint: Text(
                        'Select Type',
                        style: TextStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                      icon: Icon(Icons.keyboard_arrow_down,
                          color: Colors.grey.shade600),
                      isExpanded: true,
                      items: _typeOptions.map((String type) {
                        return DropdownMenuItem<String>(
                          value: type,
                          child: Text(
                            type,
                            style: TextStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          _selectedType = newValue;
                        });
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionField() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Description:',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 120,
            decoration: BoxDecoration(
              border: Border.all(
                color: const Color(0xFF7F7F7F),
                width: 0.5,
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            child: TextField(
              controller: _descriptionController,
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
              decoration: InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding: const EdgeInsets.all(12),
                hintStyle: TextStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              style: TextStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidateButton(ObjectCreationProvider provider) {
    return Center(
      child: Container(
        width: double.infinity,
        height: 40,
        margin: const EdgeInsets.only(left: 36),
        child: ElevatedButton(
          onPressed: () {
            _handleValidation(provider);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFF2F2F2),
            elevation: 0,
            shadowColor: Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: const BorderSide(
                color: Color(0xFFE5E5E5),
                width: .5,
              ),
            ),
          ),
          child: Text(
            'Validate',
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: const Color(0xFF333333),
            ),
          ),
        ),
      ),
    );
  }

  void _showIconSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Select Icon',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: GridView.count(
                  crossAxisCount: 4,
                  children: [
                    _buildIconOption(Icons.person, 'Person'),
                    _buildIconOption(Icons.business, 'Business'),
                    _buildIconOption(Icons.settings, 'Settings'),
                    _buildIconOption(Icons.home, 'Home'),
                    _buildIconOption(Icons.email, 'Email'),
                    _buildIconOption(Icons.phone, 'Phone'),
                    _buildIconOption(Icons.location_on, 'Location'),
                    _buildIconOption(Icons.calendar_today, 'Calendar'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildIconOption(IconData icon, String name) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedIcon = name;
        });
        Navigator.pop(context);
      },
      child: Container(
        margin: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 24, color: Colors.grey.shade700),
            const SizedBox(height: 4),
            Text(
              name,
              style: TextStyle(
                fontSize: 10,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectHeader() {
    return Row(
      children: [
        Text(
          'Object: ',
          style: TextStyle(
            fontSize: ResponsiveFontSizes.bodyLarge(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        Text(
          _objectController.text.isNotEmpty
              ? _objectController.text
              : 'Go Flow',
          style: TextStyle(
            fontSize: ResponsiveFontSizes.bodyLarge(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: const Color(0xFF007AFF),
          ),
        ),
      ],
    );
  }

  void _handleValidation(ObjectCreationProvider provider) {
    if (_objectController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter object name');
      return;
    }

    if (_selectedType == null) {
      _showErrorSnackBar('Please select a type');
      return;
    }

    if (_descriptionController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter description');
      return;
    }

    // Show expansion panels after successful validation
    setState(() {
      _showExpansionPanels = true;
    });

    _showSuccessSnackBar('Go Flow details validated successfully!');

    // Navigate to CreateSolutionOnboardingDetailsMobile page
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CreateSolutionOnboardingDetailsMobile(),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showInfoPopup(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Drag handle
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 24),

                // Message text
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 40),
                  child: Text(
                    'This Objects is already exists in your library. You need to rename the objects to proceed.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                      height: 1.4,
                    ),
                  ),
                ),
                const SizedBox(height: 36),

                // Buttons row
                Row(
                  children: [
                    // Continue button (left)
                    Expanded(
                      child: Container(
                        height: 40,
                        child: OutlinedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: OutlinedButton.styleFrom(
                            backgroundColor: Colors.white,
                            side: const BorderSide(
                              color: Color(0xFF797676),
                              width: .5,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          child: Text(
                            'Cancel',
                            style: TextStyle(
                              fontSize: ResponsiveFontSizes.titleLarge(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: const Color(0xFF333333),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Continue button (right - blue)
                    Expanded(
                      child: Container(
                        height: 40,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF007AFF),
                            elevation: 0,
                            shadowColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          child: Text(
                            'Continue',
                            style: TextStyle(
                              fontSize: ResponsiveFontSizes.titleLarge(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }
}
