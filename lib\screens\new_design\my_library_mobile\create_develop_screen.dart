import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';

// Develop Screen
class DevelopModalScreen extends StatelessWidget {
  const DevelopModalScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      body: Column(
        children: [
          // Fixed Header with Close Button
          Container(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top + 16,
              left: 16,
              right: 16,
              bottom: 24,
            ),
          
            child: Row(
              children: [
                Expanded(child: SizedBox()),
                IconButton(
                  icon: Icon(Icons.close, color: Colors.black, size: 24),
                  onPressed: () => Navigator.of(context).pop(),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(),
                ),
              
              ],
            ),
          ),
          
          // Scrollable Content
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: 600, // Max width for content
                ),
                margin: EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 32),
                    
                    // Title
                    Center(
                      child: Text(
                        'How Development Works -\nYour Custom Build Journey',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                          height: 1.3,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 40),
                    
                    // Steps
                    _buildStep(
                      stepNumber: '1',
                      title: 'Requirements Definition',
                      description: 'Capture your detailed solution requirements through our intuitive prompt interface, providing comprehensive information about your business needs.',
                    ),
                    const SizedBox(height: 32),
                    _buildStep(
                      stepNumber: '2',
                      title: 'Intelligent Parsing',
                      description: 'Our AI engine intelligently extracts and identifies the essential components from your comprehensive requirements.',
                    ),
                    const SizedBox(height: 32),
                    _buildStep(
                      stepNumber: '3',
                      title: 'Solution Architecture',
                      description: 'Seamlessly set up configurations and fine-tune your space with optimal management perfectly matching your vision.',
                    ),
                    const SizedBox(height: 32),
                    _buildStep(
                      stepNumber: '4',
                      title: 'AI-Powered Optimization',
                      description: 'Leverage smart AI recommendations to enhance and optimize your solution design for maximum effectiveness.',
                    ),
                    const SizedBox(height: 32),
                    _buildStep(
                      stepNumber: '5',
                      title: 'Industry Alignment',
                      description: 'Effortlessly import industry-standard components and best practices tailored to your business sector.',
                    ),
                    const SizedBox(height: 32),
                    _buildStep(
                      stepNumber: '6',
                      title: 'Solution Validation',
                      description: 'Thoroughly review your complete solution framework, including all components and workflow structures.',
                    ),
                    const SizedBox(height: 32),
                    _buildStep(
                      stepNumber: '7',
                      title: 'Implementation',
                      description: 'Proceed with building your solution using your fully-customized and validated framework.',
                    ),
                    const SizedBox(height: 32),
                  _buildStep(
                      stepNumber: '8',
                      title: 'Testing',
                      description: 'Test and validate your completed solution to ensure optimal performance and reliability.',
                    ),
                    
                    const SizedBox(height: 60), // Extra bottom spacing
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStep({
    required String stepNumber,
    required String title,
    required String description,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Step $stepNumber: $title',
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          description,
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: Colors.black87,
            height: 1.5,
          ),
        ),
      ],
    );
  }
}