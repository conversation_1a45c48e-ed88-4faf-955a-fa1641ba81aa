import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:nsl/converters/data_type_ui_mapping.dart';
import 'package:nsl/models/parse_validation_entity/validate_lo_model.dart';
import 'package:nsl/models/role_model.dart';
import 'package:nsl/providers/selected_object_provider.dart';
import 'package:nsl/models/solution/go_model.dart';
import 'package:nsl/services/auth_service.dart';
import 'package:nsl/models/object_creation_model.dart';
import 'package:nsl/utils/logger.dart';

import '../converters/go_text_converter.dart';
import '../models/solution/entity_attribut_details.dart';
import '../models/solution/parse_validate_go.dart';
import '../services/entity_parse_validation_service.dart';
import '../services/local_objective_validation_service.dart';

enum GoDetailsStep {
  initial,
  afterValidation,
  afterLocalObjectives,
}

class GoDetailsProvider with ChangeNotifier {
  LocalObjectiveValidationService localObjectiveValidationService =
      LocalObjectiveValidationService();
  String? savedArtifactId;
  // Controllers
  final TextEditingController solutionController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController localObjectiveController =
      TextEditingController();
  final Map<int, bool> _isPathwayCreationOpen = {};
  final Map<int, bool> _isLoInsertionOpen = {};

  bool showFooter = false; // Flag to control footer visibility
  bool _isEditMode = true;
  bool get isEditMode => _isEditMode;

  GoDetailsProvider() {
    // Add listeners to controllers to detect changes
    solutionController.addListener(_onSolutionChanged);
    descriptionController.addListener(_onDescriptionChanged);
  }

  void _onSolutionChanged() {
    _checkForChangesAndResetPublishedStatus();
  }

  void _onDescriptionChanged() {
    _checkForChangesAndResetPublishedStatus();
  }

  void toggleFooterVisibility({bool? value}) {
    showFooter = value ?? !showFooter;
    notifyListeners();
  }

  /// Updates footer visibility based on whether LOs have been created
  /// Footer should only show when there are actual LOs to work with
  void updateFooterVisibilityBasedOnLOs() {
    final hasLOs = currentGoModel?.localObjectivesList?.isNotEmpty ?? false;
    if (hasLOs && _currentStep == GoDetailsStep.afterLocalObjectives) {
      showFooter = true;
    } else {
      showFooter = false;
    }
    notifyListeners();
  }

  void updateEditMode(bool editMode) {
    _isEditMode = editMode;
    notifyListeners();
  }

  /// Sets the current step - used to preserve step state during validation
  void setCurrentStep(GoDetailsStep step) {
    _currentStep = step;

    // Update footer visibility based on the new step
    updateFooterVisibilityBasedOnLOs();

    notifyListeners();
  }

  // State variables
  GoDetailsStep _currentStep = GoDetailsStep.initial;
  PostgresRole? _selectedRole;
  List<PostgresRole> _selectedRoles = []; // Multiple roles for agent type
  bool _isValidating = false;
  String? _validationError;

  // Validation error messages
  String? _solutionValidationError;
  String? _loValidationError;
  String? _loInsertionValidationError;

  // Mock data for demonstration - in real app this would come from API
  String? _generatedDescription;
  // List<String> _localObjectives = [];
  // Local objective details state
  bool _showLocalObjectiveDetails = false;
  int? _selectedLocalObjectiveIndex;
  set loValidationError(String? error) {
    _loValidationError = error;
    notifyListeners();
  }

  set loInsertionValidationError(String? error) {
    _loInsertionValidationError = error;
    notifyListeners();
  }

  // Method to clear specific validation error
  void clearLoValidationError() {
    _loValidationError = null;
    notifyListeners();
  }

  void clearLoInsertionValidationError() {
    _loInsertionValidationError = null;
    notifyListeners();
  }

  // Local objective details dropdown selections
  Map<int, String?> _loFunctionTypes = {}; // Track function type for each LO
  Map<int, PostgresRole?> _loSelectedRoles =
      {}; // Track selected roles for each LO
  Map<int, List<PostgresRole>> _loSelectedMultipleRoles =
      {}; // Track multiple selected roles for each LO (main dropdown)
  Map<int, String?> _loExecutionRights =
      {}; // Track execution rights for each LO

  // Multiple role rows for each LO
  Map<int, List<PostgresRole?>> _loMultipleRoles =
      {}; // Track multiple roles for each LO
  Map<int, List<String?>> _loMultipleExecutionRights =
      {}; // Track multiple execution rights for each LO

  // LO-specific accordion state management
  Map<int, Map<String, dynamic>?> _loSelectedObjects =
      {}; // Track selected object for each LO
  Map<int, List<String>?> _loSelectedObjectAttributes =
      {}; // Track selected object attributes for each LO
  Map<int, List<SelectedObjectData>> _loSelectedObjectsList =
      {}; // Track multiple selected objects for each LO

  // Pathway creation state

  final Map<int, bool> _pathwayCreationStates =
      {}; // Track which LOs have pathway creation open
  Map<int, PostgresRole?> _pathwaySelectedRoles =
      {}; // Track selected roles for each LO
  Map<int, List<PostgresRole>> _pathwaySelectedMultipleRoles =
      {}; // Track multiple selected roles for each LO
  Map<int, String?> _pathwaySelectedTypes =
      {}; // Track selected types for each LO
  Map<int, String?> _pathwaySelectedLOs =
      {}; // Track selected LOs for sequential type

  // Alternative/Parallel pathway data storage - Dynamic lists
  Map<int, List<PathwayEntry>> _pathwayEntries =
      {}; // Dynamic pathway entries for each LO

  // LO insertion state
  final Map<int, bool> _loInsertionStates =
      {}; // Track which LOs have insertion text field open

  // LO edit state
  final Map<int, bool> _loEditStates = {}; // Track which LOs are in edit mode
  final Map<int, TextEditingController> _loEditControllers =
      {}; // Controllers for LO edit text fields

  // Auto-expand input stack flag
  bool _shouldAutoExpandInputStack = false;
  final Map<int, TextEditingController> _loInsertionControllers =
      {}; // Controllers for insertion text fields

  // Pending role restorations - stores role names that need to be converted to PostgresRole objects
  final Map<int, List<String>> _pendingRoleRestorations = {};

  GoModel? _currentGoModel;

  /// Getter for currentGoModel
  GoModel? get currentGoModel => _currentGoModel;

  /// Setter for currentGoModel that automatically loads data from the model
  set currentGoModel(GoModel? model) {
    _currentGoModel = model;
    if (model != null) {
      // Automatically load provider data from the GoModel when it's set
      loadProviderDataFromGoModel();
    }
  }

  /// Populates the GO details form with data from a selected GO in the left panel
  /// This method is called when user taps the "+" button on any GO in the left panel
  ///
  /// [goData] - Map containing GO information with keys:
  /// - 'name': GO name (will populate Solution field)
  /// - 'description': GO description (will populate Description field)
  /// - 'goId': GO identifier
  /// - 'localObjectives': List of String containing local objective names
  void populateFromGoSelection(Map<String, dynamic> goData) {
    // Clear any existing data
    clearAllData();

    // Set solution name (GO name)
    solutionController.text = goData['name'] ?? '';

    // Set description if available (you might want to add this to GO data structure)
    descriptionController.text = goData['description'] ?? '';

    // Create GoModel with the selected GO data
    final globalObjectives = GlobalObjectives(
      name: goData['name'] ?? '',
      description: goData['description'] ?? '',
      naturalLanguage: goData['name'] ?? '',
      roleTypes: goData['roleTypes'] ?? [], // Will be set when role is selected
      version: "1.0",
      status: "Active",
      classification: "Process",
    );

    // Create LocalObjectivesList from the GO's local objectives
    final localObjectivesList = <LocalObjectivesList>[];
    final localObjectives = goData['localObjectives'] as List<String>? ?? [];

    for (int i = 0; i < localObjectives.length; i++) {
      final loName = localObjectives[i];
      if (loName.isNotEmpty) {
        localObjectivesList.add(LocalObjectivesList(
          loNumber: i + 1,
          name: loName,
          version: "1.0",
          status: "Active",
          naturalLanguage: loName,
          goId: goData['goId'] ?? '',
          loId: "lo_${i + 1}_${DateTime.now().millisecondsSinceEpoch}",
          roleTypes: null,
          terminal: false,
          pathwayData: null,
        ));
      }
    }

    // Create and set the GoModel
    currentGoModel = GoModel(
      globalObjectives: globalObjectives,
      localObjectivesList: localObjectivesList,
    );

    // Set step to after validation to show the local objectives
    _currentStep = GoDetailsStep.afterLocalObjectives;

    // Update footer visibility since LOs have been populated
    updateFooterVisibilityBasedOnLOs();

    Logger.info(
        'GoDetailsProvider: Populated GO data - ${goData['name']} with ${localObjectives.length} local objectives');
    notifyListeners();
  }

  // Method to clear all data
  void clearAllData() {
    solutionController.clear();
    descriptionController.clear();
    localObjectiveController.clear();
    _currentStep = GoDetailsStep.initial;
    _selectedRole = null;
    _selectedRoles.clear();
    _isValidating = false;
    _validationError = null;
    _generatedDescription = null;
    _solutionValidationError = null;
    _loValidationError = null;
    _loInsertionValidationError = null;
    _showLocalObjectiveDetails = false;
    _selectedLocalObjectiveIndex = null;

    // Clear all maps
    _loFunctionTypes.clear();
    _loSelectedRoles.clear();
    _loExecutionRights.clear();
    _loMultipleRoles.clear();
    _loMultipleExecutionRights.clear();
    _loSelectedObjects.clear();
    _loSelectedObjectAttributes.clear();
    _loSelectedObjectsList.clear();
    _pathwayCreationStates.clear();
    _pathwaySelectedRoles.clear();
    _pathwaySelectedMultipleRoles.clear();
    _pathwaySelectedTypes.clear();
    _pathwaySelectedLOs.clear();
    _pathwayEntries.clear();
    _loInsertionStates.clear();

    // Clear controllers
    for (final controller in _loInsertionControllers.values) {
      controller.dispose();
    }
    _loInsertionControllers.clear();

    // Clear pending role restorations
    _pendingRoleRestorations.clear();

    // Reset footer visibility when clearing all data
    showFooter = false;

    currentGoModel = null;
  }

  /// Validates the solution field and shows error if empty
  bool validateSolutionField() {
    if (solutionController.text.trim().isEmpty) {
      _solutionValidationError = "Please enter solution name";
      notifyListeners();
      return false;
    } else {
      _solutionValidationError = null;
      notifyListeners();
      return true;
    }
  }

  /// Validates the local objective field and shows error if empty
  bool validateLocalObjectiveField() {
    if (localObjectiveController.text.trim().isEmpty) {
      _loValidationError =
          "Please enter local objectives seperated by full stop (.)";
      notifyListeners();
      return false;
    } else {
      _loValidationError = null;
      notifyListeners();
      return true;
    }
  }

  bool validateLocalObjectiveInsertionField() {
    if (_loInsertionControllers.values
        .any((controller) => controller.text.trim().isEmpty)) {
      _loInsertionValidationError =
          "Please enter local objectives seperated by full stop (.)";
      notifyListeners();
      return false;
    } else {
      _loInsertionValidationError = null;
      notifyListeners();
      return true;
    }
  }

  /// Clears validation errors
  void clearValidationErrors() {
    _solutionValidationError = null;
    _loValidationError = null;
    _loInsertionValidationError = null;
    notifyListeners();
  }

  void clearLocalObjectiveInsertionValidationErrors() {
    _solutionValidationError = null;
    _loInsertionValidationError = null;
    notifyListeners();
  }

  bool isGoValidateVisible = false;

  // Getters
  GoDetailsStep get currentStep => _currentStep;
  PostgresRole? get selectedRole => _selectedRole;
  List<PostgresRole> get selectedRoles => _selectedRoles;
  bool get isValidating => _isValidating;
  String? get validationError => _validationError;
  String? get generatedDescription => _generatedDescription;
  String? get solutionValidationError => _solutionValidationError;
  String? get loValidationError => _loValidationError;
  String? get loInsertionValidationError => _loInsertionValidationError;
  // List<String> get localObjectives => _localObjectives;

  // Pathway creation getters
  bool isPathwayCreationOpen(int index) {
    return _isPathwayCreationOpen[index] ?? false;
  }

  PostgresRole? getPathwaySelectedRole(int loIndex) =>
      _pathwaySelectedRoles[loIndex];
  List<PostgresRole> getPathwaySelectedMultipleRoles(int loIndex) =>
      _pathwaySelectedMultipleRoles[loIndex] ?? [];
  String? getPathwaySelectedType(int loIndex) => _pathwaySelectedTypes[loIndex];
  String? getPathwaySelectedLO(int loIndex) => _pathwaySelectedLOs[loIndex];

  // Alternative/Parallel pathway getters
  List<PathwayEntry> getPathwayEntries(int loIndex) =>
      _pathwayEntries[loIndex] ?? [];

  // Get specific entry by index
  PathwayEntry? getPathwayEntry(int loIndex, int entryIndex) {
    final entries = _pathwayEntries[loIndex];
    if (entries != null && entryIndex < entries.length) {
      return entries[entryIndex];
    }
    return null;
  }

  // Get count of pathway entries for a specific LO
  int getPathwayEntryCount(int loIndex) =>
      _pathwayEntries[loIndex]?.length ?? 0;

  // Backward compatibility getters (for existing UI)
  String? getPathwayFirstSelectedLO(int loIndex) =>
      getPathwayEntry(loIndex, 0)?.selectedLO;
  String? getPathwayFirstEntityAttribute(int loIndex) =>
      getPathwayEntry(loIndex, 0)?.entityAttribute;
  String? getPathwayFirstCondition(int loIndex) =>
      getPathwayEntry(loIndex, 0)?.condition;
  String? getPathwayFirstEntityAttributeAfterCondition(int loIndex) =>
      getPathwayEntry(loIndex, 0)?.entityAttributeAfterCondition;

  String? getPathwaySecondSelectedLO(int loIndex) =>
      getPathwayEntry(loIndex, 1)?.selectedLO;
  String? getPathwaySecondEntityAttribute(int loIndex) =>
      getPathwayEntry(loIndex, 1)?.entityAttribute;
  String? getPathwaySecondCondition(int loIndex) =>
      getPathwayEntry(loIndex, 1)?.condition;
  String? getPathwaySecondEntityAttributeAfterCondition(int loIndex) =>
      getPathwayEntry(loIndex, 1)?.entityAttributeAfterCondition;

  // LO insertion getters
  bool isLoInsertionOpen(int index) {
    return _isLoInsertionOpen[index] ?? false;
  }

  TextEditingController? getLoInsertionController(int loIndex) =>
      _loInsertionControllers[loIndex];

  // LO edit getters and methods
  bool isLoEditMode(int index) {
    return _loEditStates[index] ?? false;
  }

  TextEditingController? getLoEditController(int loIndex) =>
      _loEditControllers[loIndex];

  /// Toggles LO edit mode for a specific LO
  void toggleLoEditMode(int index) {
    final isCurrentlyEditing = _loEditStates[index] ?? false;

    if (!isCurrentlyEditing) {
      // Enter edit mode
      _loEditStates[index] = true;
      // Initialize controller with current LO name
      final currentLoName =
          currentGoModel?.localObjectivesList?[index].name ?? '';
      _loEditControllers[index] = TextEditingController(text: currentLoName);
    } else {
      // Exit edit mode
      _loEditStates[index] = false;
      _loEditControllers[index]?.dispose();
      _loEditControllers.remove(index);
    }

    Logger.info(
        'GoDetailsProvider: Toggled LO edit mode for LO-${index + 1}: ${_loEditStates[index]}');
    notifyListeners();
  }

  /// Saves the edited LO name
  void saveLoEdit(int index) {
    final controller = _loEditControllers[index];
    if (controller != null && currentGoModel?.localObjectivesList != null) {
      final newName = controller.text.trim();
      if (newName.isNotEmpty &&
          index < currentGoModel!.localObjectivesList!.length) {
        currentGoModel!.localObjectivesList![index].name = newName;
        Logger.info(
            'GoDetailsProvider: Updated LO-${index + 1} name to: $newName');
      }
    }
    // Exit edit mode
    toggleLoEditMode(index);
  }

  /// Cancels LO edit without saving
  void cancelLoEdit(int index) {
    // Simply exit edit mode without saving
    toggleLoEditMode(index);
  }

  // Local objective details getters
  bool get showLocalObjectiveDetails => _showLocalObjectiveDetails;
  int? get selectedLocalObjectiveIndex => _selectedLocalObjectiveIndex;

  // Local objective details dropdown getters
  String? getLoFunctionType(int loIndex) => _loFunctionTypes[loIndex];
  PostgresRole? getLoSelectedRole(int loIndex) => _loSelectedRoles[loIndex];
  List<PostgresRole> getLoSelectedMultipleRoles(int loIndex) =>
      _loSelectedMultipleRoles[loIndex] ?? [];
  String? getLoExecutionRights(int loIndex) => _loExecutionRights[loIndex];

  // Multiple roles getters
  List<PostgresRole?> getLoMultipleRoles(int loIndex) =>
      _loMultipleRoles[loIndex] ?? [];
  List<String?> getLoMultipleExecutionRights(int loIndex) =>
      _loMultipleExecutionRights[loIndex] ?? [];
  int getLoRoleRowsCount(int loIndex) => _loMultipleRoles[loIndex]?.length ?? 0;

  // LO-specific accordion state getters
  Map<String, dynamic>? getLoSelectedObject(int loIndex) =>
      _loSelectedObjects[loIndex];
  List<String>? getLoSelectedObjectAttributes(int loIndex) =>
      _loSelectedObjectAttributes[loIndex];
  List<SelectedObjectData> getLoSelectedObjectsList(int loIndex) =>
      _loSelectedObjectsList[loIndex] ?? [];
  bool hasLoSelectedObjects(int loIndex) =>
      (_loSelectedObjectsList[loIndex]?.isNotEmpty ?? false);
  int getLoSelectedObjectsCount(int loIndex) =>
      _loSelectedObjectsList[loIndex]?.length ?? 0;

  /// Get entities from GoModel's entitiesList for the specified LO
  /// This ensures all entities visible on screen come from localObjectivesList
  List<ObjectCreationModel> getLoEntitiesFromGoModel(int loIndex) {
    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      return [];
    }

    final lo = currentGoModel!.localObjectivesList![loIndex];
    return lo.entitiesList ?? [];
  }

  /// Check if LO has entities in GoModel's entitiesList
  bool hasLoEntitiesInGoModel(int loIndex) {
    final entities = getLoEntitiesFromGoModel(loIndex);
    return entities.isNotEmpty;
  }

  /// Get count of entities in GoModel's entitiesList for the specified LO
  int getLoEntitiesCountFromGoModel(int loIndex) {
    final entities = getLoEntitiesFromGoModel(loIndex);
    return entities.length;
  }

  // Collapse/Expand state for roles section
  final Map<int, bool> _loRolesSectionExpanded = {};

  bool isLoRolesSectionExpanded(int loIndex) =>
      _loRolesSectionExpanded[loIndex] ?? true;

  void toggleLoRolesSectionExpanded(int loIndex) {
    _loRolesSectionExpanded[loIndex] = !isLoRolesSectionExpanded(loIndex);
    notifyListeners();
  }

  final EntityParseValidationService _entityParseValidationService =
      EntityParseValidationService();

  ParseValidateGo? parseValidateGo;

  // Get available LOs for sequential selection (LOs after the current one)
  List<String> getAvailableLOsForSequential(int currentLoIndex) {
    if (currentLoIndex >= currentGoModel!.localObjectivesList!.length - 1)
      return [];
    return currentGoModel!.localObjectivesList!
        .asMap()
        .entries
        .where((entry) => entry.key > currentLoIndex)
        .map((entry) =>
            entry.value.name ?? '') // Return only the LO name without prefix
        .toList();
  }

  /// Sets the selected role
  void setSelectedRole(PostgresRole? role) {
    if (_selectedRole != role) {
      _selectedRole = role;
      _checkForChangesAndResetPublishedStatus();
      Logger.info(
          'GoDetailsProvider: Selected role changed to: ${role?.name ?? 'None'}');
      notifyListeners();
    }
  }

  /// Sets multiple selected roles for agent type
  void setSelectedRoles(List<PostgresRole> roles) {
    _selectedRoles = List.from(roles);
    _checkForChangesAndResetPublishedStatus();
    Logger.info(
        'GoDetailsProvider: Selected roles changed to: ${roles.map((r) => r.name).join(', ')}');

    // Auto-sync Agent Type roles to all pathway roles that are currently empty
    _syncAgentTypeRolesToPathwayRoles();

    notifyListeners();
  }

  /// Adds a role to the selected roles list
  void addSelectedRole(PostgresRole role) {
    if (!_selectedRoles.any((r) => r.roleId == role.roleId)) {
      _selectedRoles.add(role);
      _checkForChangesAndResetPublishedStatus();
      Logger.info('GoDetailsProvider: Added role: ${role.name}');
      notifyListeners();
    }
  }

  /// Removes a role from the selected roles list
  void removeSelectedRole(PostgresRole role) {
    _selectedRoles.removeWhere((r) => r.roleId == role.roleId);
    _checkForChangesAndResetPublishedStatus();
    Logger.info('GoDetailsProvider: Removed role: ${role.name}');
    notifyListeners();
  }

  /// Syncs Agent Type roles to pathway roles for all LOs that have empty pathway roles
  void _syncAgentTypeRolesToPathwayRoles() {
    if (_selectedRoles.isEmpty || currentGoModel?.localObjectivesList == null) {
      return;
    }

    for (int i = 0; i < currentGoModel!.localObjectivesList!.length; i++) {
      // Only sync if pathway roles are empty to avoid overriding user selections
      if (getPathwaySelectedMultipleRoles(i).isEmpty) {
        setPathwaySelectedMultipleRoles(i, _selectedRoles);
        Logger.info(
            'GoDetailsProvider: Auto-synced Agent Type roles to pathway for LO-${i + 1}: ${_selectedRoles.map((r) => r.name).join(', ')}');
      }
    }
  }

  /// Checks for changes and resets published status if GO was previously published
  void _checkForChangesAndResetPublishedStatus() {
    if (currentGoModel?.globalObjectives?.isPublished == true) {
      Logger.info(
          'GoDetailsProvider: Changes detected on published GO, resetting published and validated status');
      currentGoModel?.globalObjectives?.isPublished = false;
      currentGoModel?.globalObjectives?.isValidated = false;
      isGoValidateVisible = true;
      notifyListeners();
    }
  }

  /// Checks for changes and resets published status if LO was previously published
  /// This follows the same pattern as the GO implementation but for a specific LO
  void checkForChangesAndResetLoPublishedStatus(int loIndex) {
    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      return;
    }

    final lo = currentGoModel!.localObjectivesList![loIndex];

    // Reset status if LO was published OR validated (to require re-validation after changes)
    if (lo.isPublished == true || lo.isValidated == true) {
      Logger.info(
          'GoDetailsProvider: Changes detected on LO-${loIndex + 1} (published: ${lo.isPublished}, validated: ${lo.isValidated}), resetting published and validated status');
      lo.isPublished = false;
      lo.isValidated = false;
      setLoValidateVisible(loIndex, true);
      notifyListeners();
    } else {
      notifyListeners();
    }
  }

  /// Checks for changes and resets published status if LO was previously published
  /// This is a variant for modal form data changes
  void checkForChangesAndResetLoPublishedStatusFromModal(int loIndex) {
    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      return;
    }

    final lo = currentGoModel!.localObjectivesList![loIndex];

    // Reset status if LO was published OR validated (to require re-validation after changes)
    if (lo.isPublished == true || lo.isValidated == true) {
      Logger.info(
          'GoDetailsProvider (Modal): Changes detected on LO-${loIndex + 1} (published: ${lo.isPublished}, validated: ${lo.isValidated}), resetting published and validated status');
      lo.isPublished = false;
      lo.isValidated = false;
      setLoValidateVisible(loIndex, true);
      notifyListeners();
    }
  }

  /// Sets the validation visibility for a specific LO
  void setLoValidateVisible(int loIndex, bool visible) {
    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      return;
    }
    currentGoModel!.localObjectivesList![loIndex].isLoValidateVisible = visible;
  }

  /// Gets the validation visibility for a specific LO
  bool isLoValidateVisible(int loIndex) {
    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      return true; // Default to visible
    }
    return currentGoModel!.localObjectivesList![loIndex].isLoValidateVisible ??
        true;
  }

  /// Checks if LO validation button should be shown
  bool shouldShowLoValidateButton(int loIndex) {
    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      return false;
    }

    final lo = currentGoModel!.localObjectivesList![loIndex];
    return (lo.isValidated != true) && isLoValidateVisible(loIndex);
  }

  /// Checks if LO publish button should be shown
  bool shouldShowLoPublishButton(int loIndex) {
    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      return false;
    }

    final lo = currentGoModel!.localObjectivesList![loIndex];
    final go = currentGoModel!.globalObjectives;

    return (go?.isValidated == true) &&
        (go?.isPublished == true) &&
        (lo.isValidated == true) &&
        (lo.isPublished != true);
  }

  /// Checks if LO published status should be shown
  bool shouldShowLoPublishedStatus(int loIndex) {
    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      return false;
    }

    final lo = currentGoModel!.localObjectivesList![loIndex];
    return lo.isPublished == true;
  }

  /// Updates the solution name and checks for changes
  void updateSolutionName(String newName) {
    if (solutionController.text != newName) {
      solutionController.text = newName;
      _checkForChangesAndResetPublishedStatus();
      notifyListeners();
    }
  }

  /// Updates the solution description and checks for changes
  void updateSolutionDescription(String newDescription) {
    if (descriptionController.text != newDescription) {
      descriptionController.text = newDescription;
      _checkForChangesAndResetPublishedStatus();
      notifyListeners();
    }
  }

  /// Validates the solution and moves to next step
  Future<void> validateSolution() async {
    if (solutionController.text.trim().isEmpty) {
      return; // Just return without error, don't validate empty solution
    }

    _setValidating(true);
    _setValidationError(null);

    try {
      Logger.info(
          'GoDetailsProvider: Validating solution: ${solutionController.text}');

      // Simulate API call delay
      // await Future.delayed(const Duration(seconds: 2));

      // Mock response - in real app this would come from API
      _generateMockResponse();

      _currentStep = GoDetailsStep.afterValidation;
      Logger.info('GoDetailsProvider: Solution validated successfully');
    } catch (e) {
      Logger.error('GoDetailsProvider: Validation error - $e');
    } finally {
      _setValidating(false);
    }
  }

  /// Validates the solution with GoModel data and moves to next step
  Future<void> validateSolutionWithGoModel(GoModel? goModel) async {
    if (solutionController.text.trim().isEmpty) {
      return; // Just return without error, don't validate empty solution
    }

    _setValidating(true);
    _setValidationError(null);

    try {
      Logger.info(
          'GoDetailsProvider: Validating solution with GoModel: ${goModel?.globalObjectives?.name}');

      // Log the GoModel data being saved
      Logger.info(
          'GoDetailsProvider: Solution Name: ${goModel?.globalObjectives?.name}');
      Logger.info(
          'GoDetailsProvider: Solution Description: ${goModel?.globalObjectives?.description}');
      Logger.info(
          'GoDetailsProvider: Solution Agent Type: ${goModel?.globalObjectives?.roleTypes}');
      // Logger.info('GoDetailsProvider: Solution Description: ${goModel.globalObjectives?.description}');
      // Logger.info('GoDetailsProvider: Local Objectives Count: ${goModel.localObjectivesList?.length ?? 0}');

      // Log each LO name
      if (goModel?.localObjectivesList != null) {
        for (int i = 0; i < goModel!.localObjectivesList!.length; i++) {
          final lo = goModel.localObjectivesList![i];
          Logger.info('GoDetailsProvider: LO ${i + 1}: ${lo.name}');
        }
      }

      // // Simulate API call delay
      // await Future.delayed(const Duration(seconds: 2));

      // Mock response - in real app this would come from API
      _generateMockResponse();

      _currentStep = GoDetailsStep.afterValidation;
      Logger.info(
          'GoDetailsProvider: Solution with GoModel validated successfully');
    } catch (e) {
      Logger.error('GoDetailsProvider: Validation with GoModel error - $e');
    } finally {
      _setValidating(false);
    }
  }

  /// Generates mock response for demonstration
  void _generateMockResponse() {
    // Preserve existing description instead of overwriting with empty space
    // Only set generated description if the description field is currently empty
    if (descriptionController.text.trim().isEmpty) {
      _generatedDescription = ' ';
      descriptionController.text = _generatedDescription ?? '';
    } else {
      // Keep the existing description that user has entered
      _generatedDescription = descriptionController.text;
    }

    // Generate mock local objectives
    // _localObjectives = [
    //   'Type LO name with full stop (.)',
    // ];

    notifyListeners();
  }

  /// Processes local objectives from text input
  void processLocalObjectives() {
    final inputText = localObjectiveController.text.trim();

    if (inputText.isEmpty) {
      Logger.warning('GoDetailsProvider: No local objectives text provided');
      return;
    }

    // Split by full stops and clean up
    final objectives = inputText
        .split('.')
        .map((obj) => obj.trim())
        .where((obj) => obj.isNotEmpty)
        .toList();

    if (objectives.isEmpty) {
      Logger.warning('GoDetailsProvider: No valid local objectives found');
      return;
    }

    // Capitalize first letter of each objective
    // _localObjectives = objectives
    //     .map((obj) => obj.isEmpty
    //         ? obj
    //         : obj[0].toUpperCase() + obj.substring(1).toLowerCase())
    //     .toList();
    currentGoModel!.localObjectivesList = objectives
        .map((obj) => LocalObjectivesList(
            name: obj.isEmpty
                ? obj
                : obj[0].toUpperCase() + obj.substring(1).toLowerCase()))
        .toList();
    _currentStep = GoDetailsStep.afterLocalObjectives;

    // Make validate button visible - this should work for both single and multiple LOs
    isGoValidateVisible = true;

    // Update footer visibility now that LOs have been created
    updateFooterVisibilityBasedOnLOs();

    Logger.info(
        'GoDetailsProvider: Processed ${currentGoModel!.localObjectivesList!.length} local objectives: ${currentGoModel!.localObjectivesList!}');
    Logger.info(
        'GoDetailsProvider: GO Validate button is now visible: $isGoValidateVisible');
    notifyListeners();
  }

  /// Toggles pathway creation for a specific LO
  void togglePathwayCreation(int loIndex) {
    // Close LO insertion if it's open
    _isLoInsertionOpen[loIndex] = false;
    // Toggle pathway creation
    _isPathwayCreationOpen[loIndex] =
        !(_isPathwayCreationOpen[loIndex] ?? false);
    _pathwayCreationStates[loIndex] =
        !(_pathwayCreationStates[loIndex] ?? false);

    // If opening pathway creation, force load existing data from GoModel
    if (_pathwayCreationStates[loIndex]!) {
      forceLoadPathwayData(loIndex);
    } else {
      // When closing, don't clear selections if data was saved to GoModel
      // This prevents data loss when checkmark is used to close
      if (!hasPathwayData(loIndex)) {
        // Only clear if no saved data exists
        _pathwaySelectedRoles.remove(loIndex);
        _pathwaySelectedTypes.remove(loIndex);
        _pathwaySelectedLOs.remove(loIndex);
        _pathwayEntries.remove(loIndex);
      }
    }

    Logger.info(
        'GoDetailsProvider: Toggled pathway creation for LO-${loIndex + 1}: ${_pathwayCreationStates[loIndex]}');
    notifyListeners();
  }

  /// Forces pathway data to be loaded from GoModel, ensuring data is always restored
  /// This method is more aggressive than loadPathwayDataFromGoModel and ensures
  /// data is restored regardless of current state
  void forceLoadPathwayData(int loIndex) {
    Logger.info(
        'GoDetailsProvider: Force loading pathway data for LO-${loIndex + 1}');

    // Clear any existing state first to ensure clean restoration
    _pathwaySelectedRoles.remove(loIndex);
    _pathwaySelectedTypes.remove(loIndex);
    _pathwaySelectedLOs.remove(loIndex);
    _pathwayEntries.remove(loIndex);
    _pathwaySelectedMultipleRoles.remove(loIndex);

    // Load data from GoModel
    loadPathwayDataFromGoModel(loIndex);

    Logger.info(
        'GoDetailsProvider: Completed force loading pathway data for LO-${loIndex + 1}');
  }

  /// Saves all pathway data that is currently in provider state to GoModel
  /// This ensures that all LOs with pathway data are included in validation
  Future<void> saveAllPathwayDataBeforeValidation() async {
    if (currentGoModel?.localObjectivesList == null) {
      Logger.warning('GoDetailsProvider: No GoModel to save pathway data to');
      return;
    }

    Logger.info('GoDetailsProvider: Saving all pathway data before validation');
    int savedCount = 0;

    for (int i = 0; i < currentGoModel!.localObjectivesList!.length; i++) {
      // Check if this LO has pathway data in provider state (either open or with selections)
      final hasPathwayType = _pathwaySelectedTypes.containsKey(i);
      final hasPathwayRoles = _pathwaySelectedMultipleRoles.containsKey(i) &&
          _pathwaySelectedMultipleRoles[i]!.isNotEmpty;

      if (hasPathwayType && hasPathwayRoles) {
        // Validate that the pathway data is complete before saving
        if (canCompletePathwayCreation(i)) {
          savePathwayDataToGoModel(i);
          savedCount++;
          Logger.info('GoDetailsProvider: Saved pathway data for LO-${i + 1}');
        } else {
          Logger.warning(
              'GoDetailsProvider: Incomplete pathway data for LO-${i + 1}, skipping save');
        }
      }
    }

    Logger.info(
        'GoDetailsProvider: Saved pathway data for $savedCount LOs before validation');
  }

  /// Forces pathway creation to close for a specific LO (used by checkmark)
  void closePathwayCreation(int loIndex) {
    // Close LO insertion if it's open
    _isLoInsertionOpen[loIndex] = false;

    // Force close pathway creation
    _isPathwayCreationOpen[loIndex] = false;
    _pathwayCreationStates[loIndex] = false;

    Logger.info(
        'GoDetailsProvider: Forced close pathway creation for LO-${loIndex + 1}');
    notifyListeners();
  }

  /// Validates if pathway creation can be completed for a specific LO
  /// Returns true if all required fields are filled, false otherwise
  bool canCompletePathwayCreation(int loIndex) {
    return getPathwayValidationErrors(loIndex).isEmpty;
  }

  /// Gets detailed validation errors for pathway creation
  /// Returns a list of error messages for missing mandatory fields
  List<String> getPathwayValidationErrors(int loIndex) {
    List<String> errors = [];

    final selectedRole = getPathwaySelectedRole(loIndex);
    final selectedType = getPathwaySelectedType(loIndex);

    // Basic validation: role and type must be selected
    if (selectedRole == null) {
      errors.add(
          'Role selection is mandatory. Please select a role from the dropdown.');
    }

    if (selectedType == null || selectedType.isEmpty) {
      errors.add(
          'Pathway type selection is mandatory. Please select a pathway type.');
    }

    // Additional validation based on type (only if type is selected)
    if (selectedType != null && selectedType.isNotEmpty) {
      if (selectedType == 'Sequential') {
        final selectedLO = getPathwaySelectedLO(loIndex);
        if (selectedLO == null || selectedLO.isEmpty) {
          errors.add(
              'Target LO selection is mandatory for Sequential pathway. Please select a target LO.');
        }
      }

      // For Alternative/Parallel types, check if at least one entry is complete
      if (selectedType == 'Alternative' || selectedType == 'Parallel') {
        final entries = getPathwayEntries(loIndex);
        bool hasValidEntry = entries.isNotEmpty &&
            entries.any((entry) =>
                entry.selectedLO != null && entry.selectedLO!.isNotEmpty);

        if (!hasValidEntry) {
          errors.add(
              'At least one ${selectedType.toLowerCase()} pathway entry with target LO is mandatory. Please select a target LO for at least one entry.');
        }
      }

      // For Recursive type, additional validation can be added here if needed
      if (selectedType == 'Recursive') {
        // Currently no additional validation required for Recursive
        // But can be extended in the future
      }
    }

    return errors;
  }

  /// Validates specific pathway field and returns error message if invalid
  /// Returns null if field is valid
  String? getPathwayFieldValidationError(int loIndex, String fieldType) {
    switch (fieldType.toLowerCase()) {
      case 'role':
        final selectedRole = getPathwaySelectedRole(loIndex);
        if (selectedRole == null) {
          return 'Role selection is mandatory';
        }
        break;

      case 'type':
        final selectedType = getPathwaySelectedType(loIndex);
        if (selectedType == null || selectedType.isEmpty) {
          return 'Pathway type selection is mandatory';
        }
        break;

      case 'target_lo':
        final selectedType = getPathwaySelectedType(loIndex);
        if (selectedType == 'Sequential') {
          final selectedLO = getPathwaySelectedLO(loIndex);
          if (selectedLO == null || selectedLO.isEmpty) {
            return 'Target LO selection is mandatory for Sequential pathway';
          }
        }
        break;

      default:
        return null;
    }
    return null;
  }

  /// Saves pathway data to GoModel for a specific LO
  /// This method is called when the checkmark is clicked to persist the pathway data
  void savePathwayDataToGoModel(int loIndex) {
    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      Logger.warning(
          'GoDetailsProvider: Cannot save pathway data - GoModel or LO not found');
      return;
    }

    final lo = currentGoModel!.localObjectivesList![loIndex];
    final selectedType = getPathwaySelectedType(loIndex);
    final selectedRoles = getPathwaySelectedMultipleRoles(loIndex);

    if (selectedType == null || selectedRoles.isEmpty) {
      Logger.warning(
          'GoDetailsProvider: Cannot save pathway data - missing required fields');
      return;
    }

    // Create pathway data
    final pathwayData = PathwayData(
      selectedRoles: selectedRoles
          .map((role) => role.name ?? '')
          .where((name) => name.isNotEmpty)
          .toList(),
      selectedType: selectedType,
    );

    // Add type-specific data
    switch (selectedType) {
      case 'Sequential':
        final selectedLO = getPathwaySelectedLO(loIndex);
        pathwayData.sequentialData = SequentialPathwayData(
          selectedLO: selectedLO,
        );
        break;

      case 'Alternative':
        final pathwayEntries = getPathwayEntries(loIndex);
        pathwayData.alternativeData = AlternativePathwayData(
          pathwayEntries: pathwayEntries,
        );
        break;

      case 'Parallel':
        final pathwayEntries = getPathwayEntries(loIndex);
        pathwayData.parallelData = ParallelPathwayData(
          pathwayEntries: pathwayEntries,
        );
        break;

      case 'Recursive':
        pathwayData.recursiveData = RecursivePathwayData(
          isRecursive: true,
        );
        break;

      case 'Terminal':
        pathwayData.isTerminal = true;
        break;
    }

    // Save to GoModel
    lo.pathwayData = pathwayData;
    lo.terminal = selectedType == 'Terminal';

    Logger.info(
        'GoDetailsProvider: Saved pathway data to GoModel for LO-${loIndex + 1}: '
        'type=$selectedType, roles=${selectedRoles.map((r) => r.name).join(', ')}');

    notifyListeners();
  }

  /// Sets the selected role for pathway creation
  void setPathwaySelectedRole(int loIndex, PostgresRole? role) {
    _pathwaySelectedRoles[loIndex] = role;

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set pathway role for LO-${loIndex + 1}: ${role?.name}');
    notifyListeners();
  }

  /// Sets multiple selected roles for pathway creation
  void setPathwaySelectedMultipleRoles(int loIndex, List<PostgresRole> roles) {
    _pathwaySelectedMultipleRoles[loIndex] = List.from(roles);

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set pathway multiple roles for LO-${loIndex + 1}: ${roles.map((r) => r.name).join(', ')}');
    notifyListeners();
  }

  /// Sets the selected type for pathway creation
  void setPathwaySelectedType(int loIndex, String? type) {
    _pathwaySelectedTypes[loIndex] = type;

    // Clear LO selection if type changes
    if (type != 'Sequential') {
      _pathwaySelectedLOs.remove(loIndex);
    }

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set pathway type for LO-${loIndex + 1}: $type');
    notifyListeners();
  }

  /// Sets the selected LO for sequential pathway
  void setPathwaySelectedLO(int loIndex, String? selectedLO) {
    _pathwaySelectedLOs[loIndex] = selectedLO;

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set pathway LO for LO-${loIndex + 1}: $selectedLO');
    notifyListeners();
  }

  // Alternative/Parallel pathway setters - Dynamic approach
  void _ensurePathwayEntryExists(int loIndex, int entryIndex) {
    if (_pathwayEntries[loIndex] == null) {
      _pathwayEntries[loIndex] = [];
    }

    // Ensure we have enough entries
    while (_pathwayEntries[loIndex]!.length <= entryIndex) {
      _pathwayEntries[loIndex]!.add(PathwayEntry());
    }
  }

  void setPathwayEntrySelectedLO(
      int loIndex, int entryIndex, String? selectedLO) {
    _ensurePathwayEntryExists(loIndex, entryIndex);
    _pathwayEntries[loIndex]![entryIndex].selectedLO = selectedLO;

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set LO for LO-${loIndex + 1}, Entry-${entryIndex + 1}: $selectedLO');
    notifyListeners();
  }

  void setPathwayEntryEntityAttribute(
      int loIndex, int entryIndex, String? attribute) {
    _ensurePathwayEntryExists(loIndex, entryIndex);
    _pathwayEntries[loIndex]![entryIndex].entityAttribute = attribute;

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set entity attribute for LO-${loIndex + 1}, Entry-${entryIndex + 1}: $attribute');
    notifyListeners();
  }

  void setPathwayEntryCondition(
      int loIndex, int entryIndex, String? condition) {
    _ensurePathwayEntryExists(loIndex, entryIndex);
    _pathwayEntries[loIndex]![entryIndex].condition = condition;

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set condition for LO-${loIndex + 1}, Entry-${entryIndex + 1}: $condition');
    notifyListeners();
  }

  void setPathwayEntryEntityAttributeAfterCondition(
      int loIndex, int entryIndex, String? attribute) {
    _ensurePathwayEntryExists(loIndex, entryIndex);
    _pathwayEntries[loIndex]![entryIndex].entityAttributeAfterCondition =
        attribute;

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set entity attribute after condition for LO-${loIndex + 1}, Entry-${entryIndex + 1}: $attribute');
    notifyListeners();
  }

  // Add new pathway entry
  void addPathwayEntry(int loIndex) {
    if (_pathwayEntries[loIndex] == null) {
      _pathwayEntries[loIndex] = [];
    }
    _pathwayEntries[loIndex]!.add(PathwayEntry());

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Added new pathway entry for LO-${loIndex + 1}. Total entries: ${_pathwayEntries[loIndex]!.length}');
    notifyListeners();
  }

  // Remove pathway entry
  void removePathwayEntry(int loIndex, int entryIndex) {
    if (_pathwayEntries[loIndex] != null &&
        entryIndex < _pathwayEntries[loIndex]!.length) {
      _pathwayEntries[loIndex]!.removeAt(entryIndex);

      // Check for changes and reset published status
      _checkForChangesAndResetPublishedStatus();

      Logger.info(
          'GoDetailsProvider: Removed pathway entry ${entryIndex + 1} for LO-${loIndex + 1}. Remaining entries: ${_pathwayEntries[loIndex]!.length}');
      notifyListeners();
    }
  }

  // Backward compatibility setters (for existing UI)
  void setPathwayFirstSelectedLO(int loIndex, String? selectedLO) {
    setPathwayEntrySelectedLO(loIndex, 0, selectedLO);
  }

  void setPathwayFirstEntityAttribute(int loIndex, String? attribute) {
    setPathwayEntryEntityAttribute(loIndex, 0, attribute);
  }

  void setPathwayFirstCondition(int loIndex, String? condition) {
    setPathwayEntryCondition(loIndex, 0, condition);
  }

  void setPathwayFirstEntityAttributeAfterCondition(
      int loIndex, String? attribute) {
    setPathwayEntryEntityAttributeAfterCondition(loIndex, 0, attribute);
  }

  void setPathwaySecondSelectedLO(int loIndex, String? selectedLO) {
    setPathwayEntrySelectedLO(loIndex, 1, selectedLO);
  }

  void setPathwaySecondEntityAttribute(int loIndex, String? attribute) {
    setPathwayEntryEntityAttribute(loIndex, 1, attribute);
  }

  void setPathwaySecondCondition(int loIndex, String? condition) {
    setPathwayEntryCondition(loIndex, 1, condition);
  }

  void setPathwaySecondEntityAttributeAfterCondition(
      int loIndex, String? attribute) {
    setPathwayEntryEntityAttributeAfterCondition(loIndex, 1, attribute);
  }

  /// Shows local objective details for a specific LO
  void setShowLocalObjectiveDetails(int loIndex) {
    _showLocalObjectiveDetails = true;
    _selectedLocalObjectiveIndex = loIndex;
    Logger.info(
        'GoDetailsProvider: Showing local objective details for LO-${loIndex + 1}');

    // When showing LO details, footer should be hidden as it's not relevant for the detail view
    // showFooter = false;

    // Debug pathway state before navigation
    debugPathwayState(loIndex);

    notifyListeners();
  }

  /// Hides local objective details
  void hideLocalObjectiveDetails() {
    _showLocalObjectiveDetails = false;
    final previousLoIndex = _selectedLocalObjectiveIndex;
    _selectedLocalObjectiveIndex = null;
    Logger.info('GoDetailsProvider: Hiding local objective details');

    // Debug pathway state after returning from navigation
    if (previousLoIndex != null) {
      debugPathwayState(previousLoIndex);
    }

    // Update footer visibility based on current state
    updateFooterVisibilityBasedOnLOs();

    notifyListeners();
  }

  /// Sync _loSelectedObjectsList data to GoModel entitiesList before validation
  void syncSelectedObjectsToGoModel(int loIndex) {
    Logger.info(
        'GoDetailsProvider: Syncing selected objects to GoModel for LO-${loIndex + 1}');

    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      Logger.warning(
          'GoDetailsProvider: Cannot sync - GoModel or LO not found');
      return;
    }

    final lo = currentGoModel!.localObjectivesList![loIndex];
    final selectedObjects = _loSelectedObjectsList[loIndex];

    if (selectedObjects == null || selectedObjects.isEmpty) {
      Logger.info(
          'GoDetailsProvider: No selected objects to sync for LO-${loIndex + 1}');
      return;
    }

    // Initialize entitiesList if null
    lo.entitiesList ??= [];

    Logger.info(
        'GoDetailsProvider: Syncing ${selectedObjects.length} objects to GoModel');

    for (final selectedObjectData in selectedObjects) {
      final object = selectedObjectData.object;
      final attributes = selectedObjectData.attributes;
      final entityId = object.id ?? 'unknown';
      final entityName = object.name ?? 'Unknown Entity';

      // Check if entity already exists in entitiesList
      ObjectCreationModel? existingEntity;
      try {
        existingEntity = lo.entitiesList!.firstWhere(
            (entity) => entity.id == entityId || entity.name == entityName);
      } catch (e) {
        // Entity doesn't exist, will create new one
      }

      if (existingEntity != null) {
        // Entity exists - preserve existing user-entered data
        Logger.info(
            'GoDetailsProvider: Entity "$entityName" already exists, preserving user-entered data');

        // Only add new attributes that don't already exist
        final existingAttributeNames = existingEntity.attributes
                ?.map((attr) => attr.displayName ?? attr.name)
                .toSet() ??
            <String>{};

        // Filter out attributes that already exist (attributes is List<ObjectAttribute>)
        final newAttributes = attributes
            .where((attr) =>
                !existingAttributeNames.contains(attr.displayName ?? attr.name))
            .toList();

        if (newAttributes.isNotEmpty) {
          Logger.info(
              'GoDetailsProvider: Adding ${newAttributes.length} new attributes to existing entity');

          // Combine existing and new attributes (both are ObjectAttribute objects)
          final allAttributes = <ObjectAttribute>[
            ...(existingEntity.attributes ?? <ObjectAttribute>[]),
            ...newAttributes
          ];

          // Update existing entity with combined attributes
          final updatedEntity = ObjectCreationModel(
            tenant: existingEntity.tenant,
            entityDeclaration: existingEntity.entityDeclaration,
            id: existingEntity.id,
            name: existingEntity.name,
            displayName: entityName,
            type: existingEntity.type ?? 'entity',
            description: existingEntity.description ?? 'Entity from library',
            businessPurpose: existingEntity.businessPurpose,
            businessDomain: existingEntity.businessDomain,
            category: existingEntity.category,
            archivalStrategy: existingEntity.archivalStrategy,
            colorTheme: existingEntity.colorTheme,
            icon: existingEntity.icon,
            tags: existingEntity.tags,
            attributes:
                allAttributes, // Combined attributes (preserving user data)
            relationships: existingEntity.relationships,
            businessRules: existingEntity.businessRules,
            enumValues: existingEntity.enumValues,
            securityClassification: existingEntity.securityClassification,
            systemPermissions: existingEntity.systemPermissions,
            roleSystemPermissions: existingEntity.roleSystemPermissions,
            uiProperties: existingEntity.uiProperties,
            confidence: existingEntity.confidence,
            extractionMethod: existingEntity.extractionMethod,
            completionScore: existingEntity.completionScore,
            configurationStatus: existingEntity.configurationStatus,
            createdAt: existingEntity.createdAt,
            updatedAt: DateTime.now(),
            isEntityValidatedSaved: existingEntity.isEntityValidatedSaved,
            isEntityAttributesValidatedSaved:
                existingEntity.isEntityAttributesValidatedSaved,
            isEntityRelationshipsValidatedSaved:
                existingEntity.isEntityRelationshipsValidatedSaved,
            isEntityBusinessRulesValidatedSaved:
                existingEntity.isEntityBusinessRulesValidatedSaved,
            isEntityEnumValuesValidatedSaved:
                existingEntity.isEntityEnumValuesValidatedSaved,
            isEntitySecurityClassificationValidatedSaved:
                existingEntity.isEntitySecurityClassificationValidatedSaved,
            isEntitySystemPropertiesValidatedSaved:
                existingEntity.isEntitySystemPropertiesValidatedSaved,
            attributesExpanded: existingEntity.attributesExpanded,
            relationshipsExpanded: existingEntity.relationshipsExpanded,
            enumValuesExpanded: existingEntity.enumValuesExpanded,
          );

          // Replace existing entity
          final index = lo.entitiesList!.indexWhere(
              (entity) => entity.id == entityId || entity.name == entityName);
          lo.entitiesList![index] = updatedEntity;
          Logger.info(
              'GoDetailsProvider: Updated existing entity: $entityName with ${allAttributes.length} attributes (preserved user data)');
        } else {
          Logger.info(
              'GoDetailsProvider: No new attributes to add for entity "$entityName"');
        }
      } else {
        // Create new entity with default attribute values
        Logger.info(
            'GoDetailsProvider: Creating new entity "$entityName" with default attribute values');

        // final List<ObjectAttribute> objectAttributes = [];
        // for (final attributeName in attributes) {
        //   objectAttributes.add(ObjectAttribute(
        //     name: attributeName,
        //     displayName: attributeName,
        //     dataSource: 'User', // Default data source
        //     functionType: 'String', // Default function type
        //     status: 'required',
        //     uiControl: 'String',
        //     helperText: '',
        //     errorMessage: '',
        //     dataType: 'String',
        //     required: true,
        //   ));
        // }

        final newEntity = ObjectCreationModel(
          id: entityId,
          name: entityName,
          displayName: entityName,
          type: 'entity',
          description: 'Entity from library',
          attributes: attributes,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        lo.entitiesList!.add(newEntity);
        Logger.info(
            'GoDetailsProvider: Added new entity: $entityName with ${attributes.length} attributes (default values)');
      }
    }

    Logger.info(
        'GoDetailsProvider: Completed syncing selected objects to GoModel for LO-${loIndex + 1}');
    Logger.info(
        'GoDetailsProvider: GoModel entitiesList now has ${lo.entitiesList!.length} entities');
  }

  /// Validates the current local objective
  /// Returns a map with 'clientErrors' (List<String>) and 'apiError' (String?)
  /// clientErrors: List of client-side validation errors (empty if none)
  /// apiError: API validation error message (null if successful)
  Future<Map<String, dynamic>> validateLocalObjective() async {
    if (_selectedLocalObjectiveIndex == null) {
      Logger.error(
          'GoDetailsProvider: No local objective selected for validation');
      return {
        'clientErrors': ['No local objective selected for validation'],
        'apiError': null
      };
    }

    try {
      Logger.info(
          'GoDetailsProvider: Validating local objective LO-${_selectedLocalObjectiveIndex! + 1}');

      final loIndex = _selectedLocalObjectiveIndex!;

      // Sync selected objects to GoModel before validation
      syncSelectedObjectsToGoModel(loIndex);

      // Perform all validation checks
      final validationResults =
          await _performAllLocalObjectiveValidations(loIndex);

      // If any validation failed, return client-side error messages
      if (validationResults.isNotEmpty) {
        for (final error in validationResults) {
          Logger.error('GoDetailsProvider: $error');
        }
        return {'clientErrors': validationResults, 'apiError': null};
      }

      // All validations passed, call the API
      final apiResult = await _callLocalObjectiveValidationAPI(loIndex);

      if (apiResult == null) {
        Logger.info(
            'GoDetailsProvider: Local objective LO-${loIndex + 1} validated successfully via API');
        notifyListeners();
        return {
          'clientErrors': [],
          'apiError': null
        }; // Return empty results for success
      } else {
        Logger.error(
            'GoDetailsProvider: Local objective LO-${loIndex + 1} API validation failed');
        return {
          'clientErrors': [],
          'apiError': apiResult
        }; // Return API error message
      }
    } catch (e) {
      Logger.error('GoDetailsProvider: Error validating local objective: $e');
      return {
        'clientErrors': [],
        'apiError': 'Error validating local objective: $e'
      };
    }
  }

  /// Performs all local objective validation checks
  Future<List<String>> _performAllLocalObjectiveValidations(int loIndex) async {
    final errors = <String>[];

    // 1. Validate inputstack data (entitiesList)
    final inputstackValidation = _validateInputStackData(loIndex);
    if (inputstackValidation != null) {
      errors.add('LO-${loIndex + 1} validation failed - $inputstackValidation');
    }

    // 2. Validate function type
    final functionTypeValidation = _validateFunctionType(loIndex);
    if (functionTypeValidation != null) {
      errors
          .add('LO-${loIndex + 1} validation failed - $functionTypeValidation');
    }

    // 3. Validate role selection
    final roleValidation = _validateRoleSelection(loIndex);
    if (roleValidation != null) {
      errors.add('LO-${loIndex + 1} validation failed - $roleValidation');
    }

    // 4. Validate attributes row data
    final attributesValidation = _validateAttributesRowData(loIndex);
    if (attributesValidation != null) {
      errors.add('LO-${loIndex + 1} validation failed - $attributesValidation');
    }

    return errors;
  }

  /// Validates input stack data comprehensively - checks all user-entered attribute data
  String? _validateInputStackData(int loIndex) {
    final localObjectivesList = currentGoModel?.localObjectivesList;
    if (localObjectivesList == null || loIndex >= localObjectivesList.length) {
      return 'Local objective not found';
    }

    final localObjective = localObjectivesList[loIndex];
    final entitiesList = localObjective.entitiesList;

    if (entitiesList == null || entitiesList.isEmpty) {
      return 'No input stack data found - please add entities from library';
    }

    Logger.info(
        'GoDetailsProvider: Validating input stack data for LO-${loIndex + 1}');
    Logger.info(
        'GoDetailsProvider: Found ${entitiesList.length} entities in input stack');

    // Validate each entity in the input stack
    for (int entityIndex = 0;
        entityIndex < entitiesList.length;
        entityIndex++) {
      final entity = entitiesList[entityIndex];
      final attributes = entity.attributes;

      if (attributes == null || attributes.isEmpty) {
        return 'Entity "${entity.displayName ?? entity.name}" in input stack has no attributes defined';
      }

      Logger.info(
          'GoDetailsProvider: Validating entity "${entity.displayName ?? entity.name}" with ${attributes.length} attributes');

      // Validate each attribute in the entity
      for (int attrIndex = 0; attrIndex < attributes.length; attrIndex++) {
        final attribute = attributes[attrIndex];
        final attrName =
            attribute.displayName ?? attribute.name ?? 'Unknown Attribute';

        Logger.info('GoDetailsProvider: Validating attribute "$attrName"');

        // Comprehensive validation for each attribute field
        final validationResult =
            _validateSingleAttribute(attribute, entity, attrIndex);
        if (validationResult != null) {
          return validationResult;
        }
      }

      Logger.info(
          'GoDetailsProvider: Entity "${entity.displayName ?? entity.name}" input stack validation passed');
    }

    Logger.info(
        'GoDetailsProvider: Input stack validation completed successfully for LO-${loIndex + 1}');
    return null; // Validation passed
  }

  /// Validates a single attribute with comprehensive checks
  String? _validateSingleAttribute(
      ObjectAttribute attribute, ObjectCreationModel entity, int attrIndex) {
    final attrName =
        attribute.displayName ?? attribute.name ?? 'Unknown Attribute';
    final entityName = entity.displayName ?? entity.name ?? 'Unknown Entity';

    // 1. Basic attribute information validation
    if (attribute.name == null || attribute.name!.trim().isEmpty) {
      return 'Attribute ${attrIndex + 1} in entity "$entityName" has no name defined';
    }

    if (attribute.displayName == null ||
        attribute.displayName!.trim().isEmpty) {
      return 'Attribute "$attrName" in entity "$entityName" has no display name';
    }

    // 2. Data source validation
    if (attribute.dataSource == null || attribute.dataSource!.trim().isEmpty) {
      return 'Attribute "$attrName" in entity "$entityName" has no data source selected';
    }

    // Validate data source is one of the expected values
    final validDataSources = [
      'User',
      'Nested Function',
      'Mapping',
      'Constant',
      'Condition Potential',
    ];
    if (!validDataSources.contains(attribute.dataSource)) {
      return 'Attribute "$attrName" in entity "$entityName" has invalid data source "${attribute.dataSource}"';
    }

    // 3. Function type validation - REMOVED as per user request
    // Function type validation for attributes has been removed

    // 4. Status validation
    if (attribute.status == null || attribute.status!.trim().isEmpty) {
      return 'Attribute "$attrName" in entity "$entityName" has no status defined';
    }

    final validStatuses = ['required', 'optional'];
    if (!validStatuses.contains(attribute.status!.toLowerCase())) {
      return 'Attribute "$attrName" in entity "$entityName" has invalid status "${attribute.status}"';
    }

    // 5. UI Control validation
    if (attribute.uiControl == null || attribute.uiControl!.trim().isEmpty) {
      return 'Attribute "$attrName" in entity "$entityName" has no UI control type defined';
    }

    final validUIControls = UIControllerTypeMapping.getAllUiControllers();
    if (!validUIControls.contains(attribute.uiControl)) {
      return 'Attribute "$attrName" in entity "$entityName" has invalid UI control type "${attribute.uiControl}"';
    }

    // 6. Data type validation
    // if (attribute.dataType == null || attribute.dataType!.trim().isEmpty) {
    //   return 'Attribute "$attrName" in entity "$entityName" has no data type defined';
    // }

    // final validDataTypes = [
    //   'string',
    //   'integer',
    //   'decimal',
    //   'boolean',
    //   'date',
    //   'datetime',
    //   'text',
    //   'Enum',
    //   'Object',
    //   'Array',
    //   'MultiValue'
    // ];
    // if (!validDataTypes
    //     .map(
    //       (e) => e.toLowerCase(),
    //     )
    //     .toList()
    //     .contains(attribute.dataType?.toLowerCase())) {
    //   return 'Attribute "$attrName" in entity "$entityName" has invalid data type "${attribute.dataType}"';
    // }

    // 7. Required field consistency validation
    // if (attribute.status!.toLowerCase() == 'required' &&
    //     !(attribute.required ?? false)) {
    //   return 'Attribute "$attrName" in entity "$entityName" is marked as required but required flag is not set';
    // }

    // if (attribute.status!.toLowerCase() == 'optional' &&
    //     (attribute.required ?? false)) {
    //   return 'Attribute "$attrName" in entity "$entityName" is marked as optional but required flag is set';
    // }

    // 8. Data source and function type consistency validation - REMOVED as per user request
    // Function type consistency validation for attributes has been removed

    // 9. UI Control and Data Type consistency validation
    if (attribute.uiControl == 'Number' &&
        attribute.dataType != 'Integer' &&
        attribute.dataType != 'Float') {
      return 'Attribute "$attrName" in entity "$entityName" has "Number" UI control but data type "${attribute.dataType}" is not numeric';
    }

    if (attribute.uiControl == 'Date' &&
        attribute.dataType != 'Date' &&
        attribute.dataType != 'DateTime') {
      return 'Attribute "$attrName" in entity "$entityName" has "Date" UI control but data type "${attribute.dataType}" is not date-related';
    }

    if (attribute.uiControl == 'Boolean' && attribute.dataType != 'Boolean') {
      return 'Attribute "$attrName" in entity "$entityName" has "Boolean" UI control but data type "${attribute.dataType}" is not boolean';
    }

    // 10. Helper text and error message validation (warnings only)
    if (attribute.helperText == null || attribute.helperText!.trim().isEmpty) {
      Logger.warning(
          'GoDetailsProvider: Attribute "$attrName" has no helper text (recommended for better UX)');
    }

    if (attribute.errorMessage == null ||
        attribute.errorMessage!.trim().isEmpty) {
      Logger.warning(
          'GoDetailsProvider: Attribute "$attrName" has no error message (recommended for better UX)');
    }

    Logger.info('GoDetailsProvider: Attribute "$attrName" validation passed');
    return null; // Validation passed
  }

  /// Validates attributes row data with comprehensive user-entered data validation
  String? _validateAttributesRowData(int loIndex) {
    // This method now delegates to the comprehensive input stack validation
    return _validateInputStackData(loIndex);
  }

  /// Validates function type selection
  String? _validateFunctionType(int loIndex) {
    final functionType = _loFunctionTypes[loIndex];
    if (functionType == null || functionType.trim().isEmpty) {
      return 'Function type not selected';
    }

    return null; // Validation passed
  }

  /// Validates role selection
  String? _validateRoleSelection(int loIndex) {
    final selectedRole = _loSelectedRoles[loIndex];
    final multipleRoles = _loMultipleRoles[loIndex];

    bool hasRoleSelected = false;

    // Check primary role
    if (selectedRole != null) {
      hasRoleSelected = true;
    }

    // Check multiple roles if they exist
    if (multipleRoles != null && multipleRoles.isNotEmpty) {
      for (var role in multipleRoles) {
        if (role != null) {
          hasRoleSelected = true;
          break;
        }
      }
    }

    if (!hasRoleSelected) {
      return 'No role selected';
    }

    return null; // Validation passed
  }

  /// Calls the local objective validation API
  /// Returns null on success, error message on failure
  Future<String?> _callLocalObjectiveValidationAPI(int loIndex) async {
    try {
      final localObjectivesList = currentGoModel?.localObjectivesList;
      if (localObjectivesList == null ||
          loIndex >= localObjectivesList.length) {
        Logger.error(
            'GoDetailsProvider: Local objective not found for API validation');
        return 'Local objective not found for API validation';
      }

      final localObjective = localObjectivesList[loIndex];
      final functionType = _loFunctionTypes[loIndex];
      final globalObjectives = currentGoModel?.globalObjectives;

      // Collect all selected roles
      final selectedRoles = <PostgresRole?>[];

      // Add multiple selected roles from main dropdown
      final multipleSelectedRoles = _loSelectedMultipleRoles[loIndex];
      if (multipleSelectedRoles != null && multipleSelectedRoles.isNotEmpty) {
        selectedRoles.addAll(multipleSelectedRoles);
      }

      // Add primary role if selected (for backward compatibility)
      final selectedRole = _loSelectedRoles[loIndex];
      if (selectedRole != null) {
        selectedRoles.add(selectedRole);
      }

      // Add multiple roles from additional rows if they exist
      final multipleRoles = _loMultipleRoles[loIndex];
      if (multipleRoles != null && multipleRoles.isNotEmpty) {
        selectedRoles.addAll(multipleRoles.where((r) => r != null));
      }

      // Import and use the validation service
      final validationService = LocalObjectiveValidationService();
      final result = await validationService.validateLocalObjective(
        loIndex: loIndex,
        localObjective: localObjective,
        globalObjectives: globalObjectives,
        functionType: functionType,
        selectedRoles: selectedRoles,
      );

      Logger.info('GoDetailsProvider: API validation result: $result');

      // Check if the API call was successful
      if (result['success'] == true) {
        currentGoModel?.localObjectivesList?[loIndex].isValidated = true;
        setLoValidateVisible(loIndex,
            false); // Hide validation button after successful validation
        Logger.info(
            'GoDetailsProvider: LO validation and save successful - validation button hidden');
        return null; // API call successful
      } else {
        final errorMessage = result['message'] ?? 'API validation failed';
        ValidateLoModel? errors = result['save_data'];
        Logger.error('GoDetailsProvider: API validation failed: $errorMessage');
        return errors?.validationErrors?.map((e) => e.message).join('\n') ??
            errorMessage; // API call failed
      }
    } catch (e) {
      Logger.error('GoDetailsProvider: API validation failed: $e');
      return 'API validation failed: $e'; // API call failed
    }
  }

  Future<String?> publishLo(int loIndex) async {
    try {
      final localObjectivesList = currentGoModel?.localObjectivesList;
      if (localObjectivesList == null ||
          loIndex >= localObjectivesList.length) {
        Logger.error(
            'GoDetailsProvider: Local objective not found for publishLo API validation');
        return 'Local objective not found for publishLo API validation';
      }

      final localObjective = localObjectivesList[loIndex];
      final functionType = _loFunctionTypes[loIndex];
      final globalObjectives = currentGoModel?.globalObjectives;

      // Collect all selected roles
      final selectedRoles = <PostgresRole?>[];

      // Add multiple selected roles from main dropdown
      final multipleSelectedRoles = _loSelectedMultipleRoles[loIndex];
      if (multipleSelectedRoles != null && multipleSelectedRoles.isNotEmpty) {
        selectedRoles.addAll(multipleSelectedRoles);
      }

      // Add primary role if selected (for backward compatibility)
      final selectedRole = _loSelectedRoles[loIndex];
      if (selectedRole != null) {
        selectedRoles.add(selectedRole);
      }

      // Add multiple roles if they exist
      final multipleRoles = _loMultipleRoles[loIndex];
      if (multipleRoles != null && multipleRoles.isNotEmpty) {
        selectedRoles.addAll(multipleRoles.where((r) => r != null));
      }

      // Import and use the validation service
      final validationService = LocalObjectiveValidationService();
      final result = await validationService.publishLo(
        localObjective: localObjective,
        globalObjectives: globalObjectives,
        functionType: functionType,
        selectedRoles: selectedRoles,
      );

      Logger.info('GoDetailsProvider: publishLo result: $result');

      // Check if the API call was successful
      if (result['success'] == true) {
        currentGoModel?.localObjectivesList?[loIndex].isPublished = true;
        Logger.info('GoDetailsProvider:   publishLo  successful');
        bool allLoPublished = currentGoModel?.localObjectivesList!
                .every((lo) => lo.isPublished == true) ??
            false;
        bool doesLoHasMapping = currentGoModel?.localObjectivesList!.any((lo) =>
                lo.entitiesList!.isNotEmpty &&
                lo.entitiesList!.any((entity) =>
                    entity.attributes!.isNotEmpty &&
                    entity.attributes!.any(
                        (attribute) => attribute.dataSource == 'Mapping'))) ??
            false;

        if (allLoPublished && doesLoHasMapping) {
          final mappingValidate = await validationService.mappingValidate(
            localObjectives: currentGoModel!.localObjectivesList!,
          );
          if (mappingValidate['success'] == true) {
            Logger.info('GoDetailsProvider:   mappingValidate  successful');
            final mappingDeploy = await validationService.mappingPublish(
              localObjectives: currentGoModel!.localObjectivesList!,
            );
            if (mappingDeploy['success'] == true) {
              Logger.info('GoDetailsProvider:   mappingDeploy  successful');
              return null; // API call successful
            } else {
              Logger.error('GoDetailsProvider:   mappingDeploy  failed');
              return mappingDeploy['message']; // API call failed
            }
          } else {
            Logger.error('GoDetailsProvider:   mappingValidate  failed');
            return mappingValidate['message']; // API call failed
          }
        }
        return null; // API call successful
      } else {
        final errorMessage =
            result['message']?.messages?.join('\n') ?? 'API validation failed';
        Logger.error('GoDetailsProvider: publishLo failed: $errorMessage');
        return errorMessage; // API call failed
      }
    } catch (e) {
      Logger.error('GoDetailsProvider: publishLo failed: $e');
      return 'API validation failed: $e'; // API call failed
    }
  }

  /// Cancels the current local objective details view
  void cancelLocalObjectiveDetails() {
    Logger.info('GoDetailsProvider: Canceling local objective details');
    hideLocalObjectiveDetails();
  }

  /// Sets the function type for a specific LO
  void setLoFunctionType(int loIndex, String? functionType) {
    _loFunctionTypes[loIndex] = functionType;
    Logger.info(
        'GoDetailsProvider: Set function type for LO-${loIndex + 1}: $functionType');

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    // Sync to GoModel
    syncProviderDataToGoModel();
    notifyListeners();
  }

  /// Sets the selected role for a specific LO
  void setLoSelectedRole(int loIndex, PostgresRole? role) {
    _loSelectedRoles[loIndex] = role;
    Logger.info(
        'GoDetailsProvider: Set role for LO-${loIndex + 1}: ${role?.name}');

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    // Sync to GoModel
    syncProviderDataToGoModel();
    notifyListeners();
  }

  /// Sets multiple selected roles for a specific LO (main dropdown)
  void setLoSelectedMultipleRoles(int loIndex, List<PostgresRole> roles) {
    _loSelectedMultipleRoles[loIndex] = List.from(roles);
    Logger.info(
        'GoDetailsProvider: Set multiple roles for LO-${loIndex + 1}: ${roles.map((r) => r.name).join(', ')}');

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    // Sync to GoModel
    syncProviderDataToGoModel();
    notifyListeners();
  }

  /// Syncs pathway roles to LO roles automatically
  /// First checks pathway-specific roles, then falls back to global agent type roles
  void syncPathwayRolesToLoRoles(int loIndex) {
    final pathwayRoles = getPathwaySelectedMultipleRoles(loIndex);

    if (pathwayRoles.isNotEmpty) {
      // Use pathway-specific roles if available
      setLoSelectedMultipleRoles(loIndex, pathwayRoles);
      Logger.info(
          'GoDetailsProvider: Synced pathway roles to LO-${loIndex + 1}: ${pathwayRoles.map((r) => r.name).join(', ')}');
    } else if (selectedRoles.isNotEmpty) {
      // Fall back to global agent type roles if no pathway-specific roles
      setLoSelectedMultipleRoles(loIndex, selectedRoles);
      Logger.info(
          'GoDetailsProvider: Synced global agent type roles to LO-${loIndex + 1}: ${selectedRoles.map((r) => r.name).join(', ')}');
    }
  }

  /// Sets the execution rights for a specific LO
  void setLoExecutionRights(int loIndex, String? executionRights) {
    _loExecutionRights[loIndex] = executionRights;
    Logger.info(
        'GoDetailsProvider: Set execution rights for LO-${loIndex + 1}: $executionRights');

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    // Sync to GoModel
    syncProviderDataToGoModel();
    notifyListeners();
  }

  /// Adds a new role row for a specific LO
  void addLoRoleRow(int loIndex) {
    if (_loMultipleRoles[loIndex] == null) {
      _loMultipleRoles[loIndex] = [];
      _loMultipleExecutionRights[loIndex] = [];
    }
    _loMultipleRoles[loIndex]!.add(null);
    _loMultipleExecutionRights[loIndex]!.add(null);

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Added role row for LO-${loIndex + 1}. Total rows: ${_loMultipleRoles[loIndex]!.length}');
    notifyListeners();
  }

  /// Sets a role for a specific row in a specific LO
  void setLoMultipleRole(int loIndex, int rowIndex, PostgresRole? role) {
    if (_loMultipleRoles[loIndex] == null) {
      _loMultipleRoles[loIndex] = [];
    }
    // Ensure the list is large enough
    while (_loMultipleRoles[loIndex]!.length <= rowIndex) {
      _loMultipleRoles[loIndex]!.add(null);
    }
    _loMultipleRoles[loIndex]![rowIndex] = role;

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set role for LO-${loIndex + 1}, row ${rowIndex + 1}: ${role?.name}');
    notifyListeners();
  }

  /// Sets execution rights for a specific row in a specific LO
  void setLoMultipleExecutionRights(
      int loIndex, int rowIndex, String? executionRights) {
    if (_loMultipleExecutionRights[loIndex] == null) {
      _loMultipleExecutionRights[loIndex] = [];
    }
    // Ensure the list is large enough
    while (_loMultipleExecutionRights[loIndex]!.length <= rowIndex) {
      _loMultipleExecutionRights[loIndex]!.add(null);
    }
    _loMultipleExecutionRights[loIndex]![rowIndex] = executionRights;

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set execution rights for LO-${loIndex + 1}, row ${rowIndex + 1}: $executionRights');
    notifyListeners();
  }

  /// LO-specific accordion state setters
  void setLoSelectedObject(
      int loIndex, Map<String, dynamic> object, List<String> attributes) {
    _loSelectedObjects[loIndex] = object;
    _loSelectedObjectAttributes[loIndex] = attributes;

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Set selected object for LO-${loIndex + 1}: ${object['name'] ?? object['displayName']}');
    notifyListeners();
  }

  /// Add an attribute to LO with entity and attribute existence checking
  /// Returns a result indicating the action taken
  Map<String, dynamic> addLoSelectedObjectWithCheck(int loIndex,
      ObjectCreationModel object, List<ObjectAttribute> attributes) {
    Logger.info(
        '🔥 GoDetailsProvider.addLoSelectedObjectWithCheck called for LO-${loIndex + 1}');
    Logger.info('📦 Object: ${object.displayName ?? object.name}');
    Logger.info('📋 Attributes: $attributes');

    if (_loSelectedObjectsList[loIndex] == null) {
      _loSelectedObjectsList[loIndex] = [];
      Logger.info('✨ Created new list for LO-${loIndex + 1}');
    }

    // Extract base entity ID (should now be the original entity ID without attribute suffix)
    final objectEntityId = object.id?.toString() ?? '';

    // Find existing entity in the selected objects list
    int existingEntityIndex = -1;
    SelectedObjectData? existingEntity;

    for (int i = 0; i < _loSelectedObjectsList[loIndex]!.length; i++) {
      final data = _loSelectedObjectsList[loIndex]![i];
      final dataEntityId = data.object.id?.toString() ?? '';

      // Check if this is the same entity by comparing entityId directly
      // Since we're no longer appending attribute suffixes to entityId, this should work correctly
      if (dataEntityId == objectEntityId && objectEntityId.isNotEmpty) {
        existingEntityIndex = i;
        existingEntity = data;
        break;
      }

      // Fallback: also check by name if entityId is not available
      if (objectEntityId.isEmpty &&
          data.object.name == object.name &&
          object.name != null &&
          object.name.toString().isNotEmpty) {
        existingEntityIndex = i;
        existingEntity = data;
        break;
      }
    }

    if (existingEntity != null) {
      // Entity exists - check if attribute already exists
      final newAttribute = attributes.isNotEmpty ? attributes.first : null;
      if (newAttribute != null) {
        final newAttributeName =
            newAttribute.displayName ?? newAttribute.name ?? '';
        final existingAttributeNames = existingEntity.attributes
            .map((attr) => attr.displayName ?? attr.name ?? '')
            .toSet();

        if (existingAttributeNames.contains(newAttributeName)) {
          // Attribute already exists
          return {
            'success': false,
            'action': 'attribute_exists',
            'message':
                'Attribute "$newAttributeName" already exists in this entity'
          };
        }
      }

      // Entity exists but attribute doesn't - add attribute to existing entity
      final updatedAttributes =
          List<ObjectAttribute>.from(existingEntity.attributes);
      updatedAttributes.addAll(attributes);

      final updatedEntity = SelectedObjectData(
        object: existingEntity.object,
        attributes: updatedAttributes,
        id: existingEntity.id,
      );

      _loSelectedObjectsList[loIndex]![existingEntityIndex] = updatedEntity;

      Logger.info('📝 Added attribute to existing entity');
      Logger.info(
          'GoDetailsProvider: Added attribute to existing entity for LO-${loIndex + 1}: ${object.name ?? object.displayName}');

      // Auto-expand input stack when entity or attribute is added from library
      _autoExpandInputStack();

      // Check for changes and reset published status
      // _checkForChangesAndResetPublishedStatus();

      Logger.info('🔔 Calling notifyListeners()');
      notifyListeners();

      return {
        'success': true,
        'action': 'attribute_added',
        'message': 'Added attribute to existing entity'
      };
    } else {
      // Entity doesn't exist - add new entity with attribute
      final id = object.id ?? object.name ?? "";
      final selectedObjectData = SelectedObjectData(
        object: object,
        attributes: attributes,
        id: id,
      );
      _loSelectedObjectsList[loIndex]!.add(selectedObjectData);

      Logger.info('✨ Added new entity with attribute');
      Logger.info(
          '📊 LO-${loIndex + 1} now has ${_loSelectedObjectsList[loIndex]!.length} objects');
      Logger.info(
          'GoDetailsProvider: Added new entity for LO-${loIndex + 1}: ${object.name ?? object.displayName}');

      // Auto-expand input stack when entity or attribute is added from library
      _autoExpandInputStack();

      // Check for changes and reset published status
      // _checkForChangesAndResetPublishedStatus();

      Logger.info('🔔 Calling notifyListeners()');
      notifyListeners();

      return {
        'success': true,
        'action': 'entity_added',
        'message':
            'Added new entity with attribute "${attributes.isNotEmpty ? (attributes.first.displayName ?? attributes.first.name ?? '') : ''}"'
      };
    }
  }

  void addLoSelectedObject(int loIndex, ObjectCreationModel object,
      List<ObjectAttribute> attributes) {
    Logger.info(
        '🔥 GoDetailsProvider.addLoSelectedObject called for LO-${loIndex + 1}');
    Logger.info('📦 Object: ${object.name ?? object.displayName}');
    Logger.info('📋 Attributes: $attributes');

    // Show loading state
    _setAddingEntityState(true,
        'Adding ${object.displayName ?? object.name} to LO-${loIndex + 1}...');

    // Use Future.microtask to ensure UI updates before processing
    Future.microtask(() async {
      try {
        if (_loSelectedObjectsList[loIndex] == null) {
          _loSelectedObjectsList[loIndex] = [];
          Logger.info('✨ Created new list for LO-${loIndex + 1}');
        }

        final id = object.id ?? object.name ?? "";
        final selectedObjectData = SelectedObjectData(
          object: object,
          attributes: attributes,
          id: id,
        );
        _loSelectedObjectsList[loIndex]!.add(selectedObjectData);

        Logger.info(
            '📊 LO-${loIndex + 1} now has ${_loSelectedObjectsList[loIndex]!.length} objects');
        Logger.info(
            'GoDetailsProvider: Added selected object for LO-${loIndex + 1}: ${object.name ?? object.displayName}');

        // Auto-expand input stack when entity or attribute is added from library
        _autoExpandInputStack();

        // Check for changes and reset published status
        // _checkForChangesAndResetPublishedStatus();

        // Add a small delay to show progress (optional, for better UX)
        await Future.delayed(const Duration(milliseconds: 300));

        Logger.info('🔔 Calling notifyListeners()');
        notifyListeners();
        Logger.info('✅ addLoSelectedObject completed');
      } catch (e) {
        Logger.info('❌ Error in addLoSelectedObject: $e');
        Logger.error('GoDetailsProvider: Error adding selected object: $e');
      } finally {
        // Hide loading state
        _setAddingEntityState(false);
      }
    });
  }

  /// Getter for auto-expand input stack flag
  bool get shouldAutoExpandInputStack => _shouldAutoExpandInputStack;

  /// Auto-expand the input stack accordion when entities or attributes are added from library
  void _autoExpandInputStack() {
    _shouldAutoExpandInputStack = true;
    Logger.info(
        'GoDetailsProvider: Requesting auto-expand of input stack accordion');
    notifyListeners();
  }

  /// Reset auto-expand flag after it has been handled
  void resetAutoExpandInputStack() {
    _shouldAutoExpandInputStack = false;
    notifyListeners();
  }

  /// Loading state for entity addition
  bool _isAddingEntity = false;
  String _addingEntityMessage = '';

  /// Getter for entity addition loading state
  bool get isAddingEntity => _isAddingEntity;
  String get addingEntityMessage => _addingEntityMessage;

  /// Set loading state for entity addition
  void _setAddingEntityState(bool isLoading, [String message = '']) {
    _isAddingEntity = isLoading;
    _addingEntityMessage = message;
    notifyListeners();
  }

  void removeLoSelectedObject(int loIndex, String objectId) {
    if (_loSelectedObjectsList[loIndex] != null) {
      _loSelectedObjectsList[loIndex]!
          .removeWhere((data) => data.id == objectId);

      // Check for changes and reset published status
      // _checkForChangesAndResetPublishedStatus();

      Logger.info(
          'GoDetailsProvider: Removed selected object for LO-${loIndex + 1}: $objectId');
      notifyListeners();
    }
  }

  /// Remove a specific attribute from an entity in the selected objects list
  void removeLoSelectedObjectAttribute(
      int loIndex, String objectId, String attributeName) {
    Logger.info('🗑️ GoDetailsProvider.removeLoSelectedObjectAttribute called');
    Logger.info('   LO Index: $loIndex');
    Logger.info('   Object ID: $objectId');
    Logger.info('   Attribute: $attributeName');

    if (_loSelectedObjectsList[loIndex] == null) {
      Logger.info('❌ No selected objects list for LO-${loIndex + 1}');
      return;
    }

    // Find the entity
    final entityIndex = _loSelectedObjectsList[loIndex]!
        .indexWhere((data) => data.id == objectId);

    if (entityIndex == -1) {
      Logger.info('❌ Entity not found: $objectId');
      return;
    }

    final entity = _loSelectedObjectsList[loIndex]![entityIndex];
    final updatedAttributes = List<ObjectAttribute>.from(entity.attributes);

    // Remove the attribute by matching displayName or name
    final initialLength = updatedAttributes.length;
    updatedAttributes.removeWhere(
        (attr) => (attr.displayName ?? attr.name) == attributeName);

    if (updatedAttributes.length == initialLength) {
      Logger.info('❌ Attribute not found in entity: $attributeName');
      return;
    }

    Logger.info('✅ Removed attribute "$attributeName" from entity');

    if (updatedAttributes.isEmpty) {
      // If no attributes left, remove the entire entity
      _loSelectedObjectsList[loIndex]!.removeAt(entityIndex);
      Logger.info('🗑️ Removed entire entity (no attributes left)');
    } else {
      // Update the entity with remaining attributes
      final updatedEntity = SelectedObjectData(
        object: entity.object,
        attributes: updatedAttributes,
        id: entity.id,
      );
      _loSelectedObjectsList[loIndex]![entityIndex] = updatedEntity;
      Logger.info(
          '📝 Updated entity with ${updatedAttributes.length} remaining attributes');
    }

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Removed attribute "$attributeName" from entity "$objectId" for LO-${loIndex + 1}');
    notifyListeners();
  }

  void clearLoSelectedObjects(int loIndex) {
    _loSelectedObjects[loIndex] = null;
    _loSelectedObjectAttributes[loIndex] = null;
    _loSelectedObjectsList[loIndex]?.clear();

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Cleared selected objects for LO-${loIndex + 1}');
    notifyListeners();
  }

  /// Sync all provider data to the GoModel for persistence
  void syncProviderDataToGoModel() {
    if (currentGoModel?.localObjectivesList == null) {
      Logger.warning(
          'GoDetailsProvider: Cannot sync - GoModel or localObjectivesList is null');
      return;
    }

    Logger.info('GoDetailsProvider: Starting sync of provider data to GoModel');

    for (int i = 0; i < currentGoModel!.localObjectivesList!.length; i++) {
      final lo = currentGoModel!.localObjectivesList![i];

      // Sync function type
      if (_loFunctionTypes.containsKey(i)) {
        lo.functionType = _loFunctionTypes[i];
      }

      // Sync execution rights
      if (_loExecutionRights.containsKey(i)) {
        lo.executionRights = _loExecutionRights[i];
      }

      // Sync role types (from selected roles and multiple roles)
      final allRoles = <String>[];

      // Add single selected role
      if (_loSelectedRoles.containsKey(i) && _loSelectedRoles[i] != null) {
        allRoles.add(_loSelectedRoles[i]!.name!);
      }

      // Add multiple roles
      if (_loMultipleRoles.containsKey(i) && _loMultipleRoles[i] != null) {
        for (final role in _loMultipleRoles[i]!) {
          if (role != null &&
              role.name != null &&
              !allRoles.contains(role.name!)) {
            allRoles.add(role.name!);
          }
        }
      }

      // Update roleTypes if we have any roles
      if (allRoles.isNotEmpty) {
        lo.roleTypes = allRoles;
      }

      // Sync multiple execution rights
      final allExecutionRights = <String>[];

      // Add main execution rights
      if (_loExecutionRights.containsKey(i) && _loExecutionRights[i] != null) {
        allExecutionRights.add(_loExecutionRights[i]!);
      }

      // Add multiple execution rights
      if (_loMultipleExecutionRights.containsKey(i) &&
          _loMultipleExecutionRights[i] != null) {
        for (final executionRight in _loMultipleExecutionRights[i]!) {
          if (executionRight != null &&
              !allExecutionRights.contains(executionRight)) {
            allExecutionRights.add(executionRight);
          }
        }
      }

      // Update multipleExecutionRights if we have any execution rights
      if (allExecutionRights.isNotEmpty) {
        lo.multipleExecutionRights = allExecutionRights;
        // Also update the single executionRights field for backward compatibility
        lo.executionRights = allExecutionRights.first;
      }

      Logger.info('GoDetailsProvider: Synced data for LO-${i + 1}: '
          'functionType=${lo.functionType}, executionRights=${lo.executionRights}, '
          'roleTypes=${lo.roleTypes}, entities=${lo.entitiesList?.length ?? 0}');
    }

    Logger.info(
        'GoDetailsProvider: Completed sync of provider data to GoModel');
    notifyListeners();
  }

  /// Load data from GoModel into provider state (called when provider is initialized with existing GoModel)
  void loadProviderDataFromGoModel() {
    if (currentGoModel?.localObjectivesList == null) {
      Logger.warning(
          'GoDetailsProvider: Cannot load - GoModel or localObjectivesList is null');
      return;
    }

    Logger.info('GoDetailsProvider: Loading provider data from GoModel');

    // Clear existing provider data
    _loFunctionTypes.clear();
    _loExecutionRights.clear();
    _loSelectedRoles.clear();

    for (int i = 0; i < currentGoModel!.localObjectivesList!.length; i++) {
      final lo = currentGoModel!.localObjectivesList![i];

      // Load function type
      if (lo.functionType != null) {
        _loFunctionTypes[i] = lo.functionType;
      }

      // Load execution rights
      if (lo.executionRights != null) {
        _loExecutionRights[i] = lo.executionRights;
      }

      // Load multiple execution rights
      if (lo.multipleExecutionRights != null &&
          lo.multipleExecutionRights!.isNotEmpty) {
        // Skip the first one as it's already loaded as the main execution rights
        if (lo.multipleExecutionRights!.length > 1) {
          _loMultipleExecutionRights[i] =
              lo.multipleExecutionRights!.skip(1).toList();
        }
      }

      // Load role types and populate roles dropdowns
      if (lo.roleTypes != null && lo.roleTypes!.isNotEmpty) {
        // Auto-populate roles from LO level data
        _initializeRolesFromLoData(i, lo.roleTypes!);
      }
      //       // Load pathway data if it exists
      // loadPathwayDataFromGoModel(i);

      Logger.info('GoDetailsProvider: Loaded data for LO-${i + 1}: '
          'functionType=${lo.functionType}, executionRights=${lo.executionRights}, '
          'roleType=${lo.roleTypes}, entities=${lo.entitiesList?.length ?? 0}');
    }

    Logger.info(
        'GoDetailsProvider: Completed loading provider data from GoModel');
    notifyListeners();
  }

  /// Initialize roles from LO level data for auto-population
  void _initializeRolesFromLoData(int loIndex, List<String> roleNames) {
    Logger.info(
        'GoDetailsProvider: Initializing roles for LO-${loIndex + 1} from LO data: $roleNames');

    // This method will be called when roles are available from RolesProvider
    // For now, we'll store the role names to be populated later
    _pendingRoleInitialization[loIndex] = roleNames;

    Logger.info(
        'GoDetailsProvider: Stored pending role initialization for LO-${loIndex + 1}: $roleNames');
  }

  // Track pending role initializations
  final Map<int, List<String>> _pendingRoleInitialization = {};

  /// Populate roles from pending initialization when RolesProvider data is available
  void populateRolesFromPendingData(List<PostgresRole> availableRoles) {
    Logger.info(
        'GoDetailsProvider: Populating roles from pending data with ${availableRoles.length} available roles');

    for (final entry in _pendingRoleInitialization.entries) {
      final loIndex = entry.key;
      final roleNames = entry.value;

      Logger.info(
          'GoDetailsProvider: Processing LO-${loIndex + 1} with roles: $roleNames');

      // Find matching PostgresRole objects for the role names
      final matchingRoles = <PostgresRole>[];
      for (final roleName in roleNames) {
        final matchingRole = availableRoles.firstWhere(
          (role) => role.name == roleName,
          orElse: () => PostgresRole(), // Return empty role if not found
        );
        if (matchingRole.name != null) {
          matchingRoles.add(matchingRole);
        }
      }

      if (matchingRoles.isNotEmpty) {
        // Set the first role as the main selected role
        _loSelectedRoles[loIndex] = matchingRoles.first;

        // If there are additional roles, add them as multiple roles
        if (matchingRoles.length > 1) {
          _loMultipleRoles[loIndex] = matchingRoles.skip(1).toList();
          _loMultipleExecutionRights[loIndex] =
              List.filled(matchingRoles.length - 1, 'Execute Rights');
        }

        Logger.info(
            'GoDetailsProvider: Auto-populated LO-${loIndex + 1} with ${matchingRoles.length} roles');
      }
    }

    // Clear pending initializations
    _pendingRoleInitialization.clear();

    Logger.info('GoDetailsProvider: Completed role auto-population');
    notifyListeners();
  }

  /// Loads pathway data from GoModel for a specific LO back into provider state
  /// This method restores previously saved pathway dropdown values
  void loadPathwayDataFromGoModel(int loIndex) {
    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      return;
    }

    final lo = currentGoModel!.localObjectivesList![loIndex];
    final pathwayData = lo.pathwayData;

    if (pathwayData == null) {
      Logger.info(
          'GoDetailsProvider: No pathway data to load for LO-${loIndex + 1}');
      return; // No pathway data to load
    }

    Logger.info(
        'GoDetailsProvider: Loading pathway data from GoModel for LO-${loIndex + 1}');

    // Load basic pathway data
    if (pathwayData.selectedType != null) {
      _pathwaySelectedTypes[loIndex] = pathwayData.selectedType;
      Logger.info(
          'GoDetailsProvider: Restored pathway type for LO-${loIndex + 1}: ${pathwayData.selectedType}');
    }

    // Load roles - store role names for now, will be converted to objects when roles are available
    if (pathwayData.selectedRoles != null &&
        pathwayData.selectedRoles!.isNotEmpty) {
      // Store role names temporarily - they will be converted to PostgresRole objects
      // when the role field is built and roles are available
      Logger.info(
          'GoDetailsProvider: Found saved roles for LO-${loIndex + 1}: ${pathwayData.selectedRoles}');

      // Mark that this LO has role data to be restored
      _pendingRoleRestorations[loIndex] = pathwayData.selectedRoles!;
    }

    // Load type-specific data
    switch (pathwayData.selectedType) {
      case 'Sequential':
        if (pathwayData.sequentialData?.selectedLO != null) {
          _pathwaySelectedLOs[loIndex] = pathwayData.sequentialData!.selectedLO;
          Logger.info(
              'GoDetailsProvider: Restored Sequential LO for LO-${loIndex + 1}: ${pathwayData.sequentialData!.selectedLO}');
        }
        break;

      case 'Alternative':
        if (pathwayData.alternativeData?.pathwayEntries != null) {
          _pathwayEntries[loIndex] =
              List.from(pathwayData.alternativeData!.pathwayEntries);
          Logger.info(
              'GoDetailsProvider: Restored Alternative entries for LO-${loIndex + 1}: ${_pathwayEntries[loIndex]?.length} entries');
        }
        break;

      case 'Parallel':
        if (pathwayData.parallelData?.pathwayEntries != null) {
          _pathwayEntries[loIndex] =
              List.from(pathwayData.parallelData!.pathwayEntries);
          Logger.info(
              'GoDetailsProvider: Restored Parallel entries for LO-${loIndex + 1}: ${_pathwayEntries[loIndex]?.length} entries');
        }
        break;

      case 'Recursive':
        // Recursive data is just a flag, no additional data to load
        Logger.info(
            'GoDetailsProvider: Restored Recursive pathway for LO-${loIndex + 1}');
        break;

      case 'Terminal':
        // Terminal data is just a flag, no additional data to load
        Logger.info(
            'GoDetailsProvider: Restored Terminal pathway for LO-${loIndex + 1}');
        break;
    }

    Logger.info(
        'GoDetailsProvider: Completed loading pathway data for LO-${loIndex + 1}: '
        'type=${pathwayData.selectedType}, roles=${pathwayData.selectedRoles?.join(', ')}');
  }

  /// Loads pathway data from GoModel with role lookup support
  /// This method should be called when you have access to RolesProvider for proper role restoration
  void loadPathwayDataFromGoModelWithRoles(
      int loIndex, List<PostgresRole> availableRoles) {
    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      return;
    }

    final lo = currentGoModel!.localObjectivesList![loIndex];
    final pathwayData = lo.pathwayData;

    if (pathwayData == null) {
      return; // No pathway data to load
    }

    Logger.info(
        'GoDetailsProvider: Loading pathway data with roles from GoModel for LO-${loIndex + 1}');

    // Load basic pathway data
    if (pathwayData.selectedType != null) {
      _pathwaySelectedTypes[loIndex] = pathwayData.selectedType;
    }

    // Load roles with proper lookup
    final matchedRoles = <PostgresRole>[];
    if (pathwayData.selectedRoles != null &&
        pathwayData.selectedRoles!.isNotEmpty) {
      for (final roleName in pathwayData.selectedRoles!) {
        final matchedRole = availableRoles.firstWhere(
          (role) => role.name == roleName,
          orElse: () =>
              PostgresRole(name: roleName), // Fallback if role not found
        );
        matchedRoles.add(matchedRole);
      }

      if (matchedRoles.isNotEmpty) {
        _pathwaySelectedMultipleRoles[loIndex] = matchedRoles;
        // Also set single role for backward compatibility
        _pathwaySelectedRoles[loIndex] = matchedRoles.first;
      }
    }

    // Load type-specific data (same as before)
    switch (pathwayData.selectedType) {
      case 'Sequential':
        if (pathwayData.sequentialData?.selectedLO != null) {
          _pathwaySelectedLOs[loIndex] = pathwayData.sequentialData!.selectedLO;
        }
        break;

      case 'Alternative':
        if (pathwayData.alternativeData?.pathwayEntries != null) {
          _pathwayEntries[loIndex] =
              List.from(pathwayData.alternativeData!.pathwayEntries);
        }
        break;

      case 'Parallel':
        if (pathwayData.parallelData?.pathwayEntries != null) {
          _pathwayEntries[loIndex] =
              List.from(pathwayData.parallelData!.pathwayEntries);
        }
        break;

      case 'Recursive':
        // Recursive data is just a flag, no additional data to load
        break;

      case 'Terminal':
        // Terminal data is just a flag, no additional data to load
        break;
    }

    Logger.info(
        'GoDetailsProvider: Loaded pathway data with roles for LO-${loIndex + 1}: '
        'type=${pathwayData.selectedType}, roles=${matchedRoles.map((r) => r.name).join(', ')}');
  }

  /// Restores pathway data for all LOs when roles become available
  /// This should be called from the UI when RolesProvider data is loaded
  void restorePathwayDataWithRoles(List<PostgresRole> availableRoles) {
    if (currentGoModel?.localObjectivesList == null) {
      return;
    }

    Logger.info(
        'GoDetailsProvider: Restoring pathway data with roles for all LOs');

    for (int i = 0; i < currentGoModel!.localObjectivesList!.length; i++) {
      final lo = currentGoModel!.localObjectivesList![i];
      if (lo.pathwayData != null) {
        loadPathwayDataFromGoModelWithRoles(i, availableRoles);
      }
    }

    Logger.info(
        'GoDetailsProvider: Completed restoring pathway data with roles');
    notifyListeners();
  }

  /// Checks if a specific LO has saved pathway data
  bool hasPathwayData(int loIndex) {
    if (currentGoModel?.localObjectivesList == null ||
        loIndex >= currentGoModel!.localObjectivesList!.length) {
      return false;
    }

    return currentGoModel!.localObjectivesList![loIndex].pathwayData != null;
  }

  /// Checks if all LOs have complete pathway data selected
  bool areAllLosPathwaysComplete() {
    if (currentGoModel?.localObjectivesList == null ||
        currentGoModel!.localObjectivesList!.isEmpty) {
      return false;
    }

    for (int i = 0; i < currentGoModel!.localObjectivesList!.length; i++) {
      final lo = currentGoModel!.localObjectivesList![i];

      // Check if LO has pathway data
      if (lo.pathwayData == null) {
        return false;
      }

      final pathwayData = lo.pathwayData!;

      // Check if pathway type is selected
      if (pathwayData.selectedType == null ||
          pathwayData.selectedType!.isEmpty) {
        return false;
      }

      // Validate specific pathway types have required data
      switch (pathwayData.selectedType) {
        case 'Alternative':
          if (pathwayData.alternativeData == null ||
              pathwayData.alternativeData!.pathwayEntries.isEmpty) {
            return false;
          }
          // Check if each alternative entry has selectedLO
          for (var entry in pathwayData.alternativeData!.pathwayEntries) {
            if (entry.selectedLO == null || entry.selectedLO!.isEmpty) {
              return false;
            }
          }
          break;

        case 'Parallel':
          if (pathwayData.parallelData == null ||
              pathwayData.parallelData!.pathwayEntries.isEmpty) {
            return false;
          }
          // Check if each parallel entry has selectedLO
          for (var entry in pathwayData.parallelData!.pathwayEntries) {
            if (entry.selectedLO == null || entry.selectedLO!.isEmpty) {
              return false;
            }
          }
          break;

        case 'Sequential':
          if (pathwayData.sequentialData == null ||
              pathwayData.sequentialData!.selectedLO == null ||
              pathwayData.sequentialData!.selectedLO!.isEmpty) {
            return false;
          }
          break;

        case 'Terminal':
          // Terminal pathways don't need additional data validation
          break;

        case 'Recursive':
          if (pathwayData.recursiveData == null ||
              pathwayData.recursiveData!.isRecursive != true) {
            return false;
          }
          break;

        default:
          // Unknown pathway type
          return false;
      }
    }

    return true;
  }

  /// Checks if a specific LO has pending role restoration
  bool hasPendingRoleRestoration(int loIndex) {
    return _pendingRoleRestorations.containsKey(loIndex) &&
        _pendingRoleRestorations[loIndex]!.isNotEmpty;
  }

  /// Restores pending roles for a specific LO
  void restorePendingRoles(int loIndex, List<PostgresRole> availableRoles) {
    final pendingRoleNames = _pendingRoleRestorations[loIndex];
    if (pendingRoleNames == null || pendingRoleNames.isEmpty) {
      return;
    }

    Logger.info(
        'GoDetailsProvider: Restoring pending roles for LO-${loIndex + 1}: $pendingRoleNames');

    final matchedRoles = <PostgresRole>[];
    for (final roleName in pendingRoleNames) {
      final matchedRole = availableRoles.firstWhere(
        (role) => role.name == roleName,
        orElse: () =>
            PostgresRole(name: roleName), // Fallback if role not found
      );
      matchedRoles.add(matchedRole);
    }

    if (matchedRoles.isNotEmpty) {
      _pathwaySelectedMultipleRoles[loIndex] = matchedRoles;
      // Also set single role for backward compatibility
      _pathwaySelectedRoles[loIndex] = matchedRoles.first;

      Logger.info(
          'GoDetailsProvider: Successfully restored ${matchedRoles.length} roles for LO-${loIndex + 1}');
    }

    // Clear pending restoration
    _pendingRoleRestorations.remove(loIndex);

    notifyListeners();
  }

  /// Debug method to log current pathway state for a specific LO
  void debugPathwayState(int loIndex) {
    Logger.info(
        'GoDetailsProvider: DEBUG - Pathway state for LO-${loIndex + 1}:');
    Logger.info(
        '  - isPathwayCreationOpen: ${_isPathwayCreationOpen[loIndex]}');
    Logger.info('  - pathwayCreationState: ${_pathwayCreationStates[loIndex]}');
    Logger.info('  - selectedType: ${_pathwaySelectedTypes[loIndex]}');
    Logger.info('  - selectedRoles: ${_pathwaySelectedRoles[loIndex]?.name}');
    Logger.info(
        '  - selectedMultipleRoles: ${_pathwaySelectedMultipleRoles[loIndex]?.map((r) => r.name).join(', ')}');
    Logger.info('  - selectedLO: ${_pathwaySelectedLOs[loIndex]}');
    Logger.info(
        '  - pathwayEntries: ${_pathwayEntries[loIndex]?.length ?? 0} entries');
    Logger.info('  - hasPathwayData: ${hasPathwayData(loIndex)}');
    Logger.info(
        '  - hasPendingRoleRestoration: ${hasPendingRoleRestoration(loIndex)}');
    Logger.info(
        '  - pendingRoles: ${_pendingRoleRestorations[loIndex]?.join(', ') ?? 'none'}');
  }

  /// Ensures pathway data is properly restored for all LOs
  /// This method should be called when the UI needs to display pathway data
  void ensurePathwayDataRestoration() {
    if (currentGoModel?.localObjectivesList == null) {
      return;
    }

    Logger.info(
        'GoDetailsProvider: Ensuring pathway data restoration for all LOs');

    for (int i = 0; i < currentGoModel!.localObjectivesList!.length; i++) {
      final lo = currentGoModel!.localObjectivesList![i];

      // If LO has saved pathway data but provider state is empty, restore it
      if (lo.pathwayData != null && !_pathwaySelectedTypes.containsKey(i)) {
        Logger.info(
            'GoDetailsProvider: Restoring pathway data for LO-${i + 1}');
        loadPathwayDataFromGoModel(i);
      }
    }
  }

  /// Removes a role row for a specific LO
  void removeLoRoleRow(int loIndex, int rowIndex) {
    if (_loMultipleRoles[loIndex] != null &&
        rowIndex < _loMultipleRoles[loIndex]!.length) {
      _loMultipleRoles[loIndex]!.removeAt(rowIndex);
    }
    if (_loMultipleExecutionRights[loIndex] != null &&
        rowIndex < _loMultipleExecutionRights[loIndex]!.length) {
      _loMultipleExecutionRights[loIndex]!.removeAt(rowIndex);
    }

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Removed role row ${rowIndex + 1} for LO-${loIndex + 1}');
    notifyListeners();
  }

  /// Toggles LO insertion text field for a specific LO
  void toggleLoInsertion(int index) {
    // Close pathway creation if it's open
    _isPathwayCreationOpen[index] = false;
    _isLoInsertionOpen[index] = !(_isLoInsertionOpen[index] ?? false);
    final isCurrentlyOpen = _loInsertionStates[index] ?? false;

    // Close all other insertion fields first
    _closeAllLoInsertions();

    if (!isCurrentlyOpen) {
      // Open insertion for this LO
      _loInsertionStates[index] = true;
      _loInsertionControllers[index] = TextEditingController();
    }

    Logger.info(
        'GoDetailsProvider: Toggled LO insertion for LO-${index + 1}: ${_loInsertionStates[index]}');
    notifyListeners();
  }

  /// Closes all LO insertion text fields
  void _closeAllLoInsertions() {
    // Dispose controllers and clear states
    for (final controller in _loInsertionControllers.values) {
      controller.dispose();
    }
    _loInsertionControllers.clear();
    _loInsertionStates.clear();
  }

  /// Processes and inserts new LOs after the specified index
  void processLoInsertion(int afterIndex) {
    final controller = _loInsertionControllers[afterIndex];
    if (controller == null) {
      Logger.warning(
          'GoDetailsProvider: No controller found for LO insertion at index $afterIndex');
      return;
    }

    final inputText = controller.text.trim();
    if (inputText.isEmpty) {
      Logger.warning('GoDetailsProvider: No text provided for LO insertion');
      return;
    }

    // Split by dots and clean up
    final newObjectives = inputText
        .split('.')
        .map((obj) => obj.trim())
        .where((obj) => obj.isNotEmpty)
        .toList();

    if (newObjectives.isEmpty) {
      Logger.warning('GoDetailsProvider: No valid LOs found for insertion');
      return;
    }

    // Capitalize first letter of each objective
    final formattedObjectives = newObjectives
        .map((obj) => obj.isEmpty
            ? obj
            : obj[0].toUpperCase() + obj.substring(1).toLowerCase())
        .toList();
    final newLocalObjectives = formattedObjectives
        .map((obj) => LocalObjectivesList(name: obj))
        .toList();
    // Insert new LOs after the specified index
    _insertLocalObjectives(afterIndex, newLocalObjectives);

    // Check for changes and reset published status
    // _checkForChangesAndResetPublishedStatus();

    // Close the insertion field
    _loInsertionStates[afterIndex] = false;
    controller.dispose();
    _loInsertionControllers.remove(afterIndex);

    Logger.info(
        'GoDetailsProvider: Inserted ${formattedObjectives.length} LOs after index $afterIndex: $formattedObjectives');
    notifyListeners();
  }

  /// Inserts new local objectives at the specified position
  void _insertLocalObjectives(
      int afterIndex, List<LocalObjectivesList> newObjectives) {
    // Insert new objectives after the specified index
    for (int i = 0; i < newObjectives.length; i++) {
      currentGoModel!.localObjectivesList!
          .insert(afterIndex + 1 + i, newObjectives[i]);
    }

    // Update LO numbers after insertion
    _updateLoNumbers();
  }

  /// Reorder LOs by moving an LO from one position to another
  void reorderLocalObjectives(int oldIndex, int newIndex) {
    if (currentGoModel?.localObjectivesList == null ||
        oldIndex < 0 ||
        newIndex < 0 ||
        oldIndex >= currentGoModel!.localObjectivesList!.length ||
        newIndex >= currentGoModel!.localObjectivesList!.length) {
      Logger.warning('GoDetailsProvider: Invalid indices for LO reordering');
      return;
    }

    // Remove the LO from old position
    final lo = currentGoModel!.localObjectivesList!.removeAt(oldIndex);

    // Insert at new position
    currentGoModel!.localObjectivesList!.insert(newIndex, lo);

    // Update LO numbers after reordering
    _updateLoNumbers();

    Logger.info(
        'GoDetailsProvider: Reordered LO "${lo.name}" from position ${oldIndex + 1} to ${newIndex + 1}');
    notifyListeners();
  }

  /// Remove a specific LO by index with comprehensive cleanup
  void removeLocalObjective(int index) {
    if (currentGoModel?.localObjectivesList == null ||
        index < 0 ||
        index >= currentGoModel!.localObjectivesList!.length) {
      Logger.warning('GoDetailsProvider: Invalid index for LO removal');
      return;
    }

    final removedLo = currentGoModel!.localObjectivesList!.removeAt(index);

    // Clean up all associated state data for this LO
    _cleanupLoStateData(index);

    // Update LO numbers after removal
    _updateLoNumbers();

    // Update footer visibility based on remaining LOs
    updateFooterVisibilityBasedOnLOs();

    // Check for changes and reset published status
    _checkForChangesAndResetPublishedStatus();

    Logger.info(
        'GoDetailsProvider: Removed LO "${removedLo.name}" from position ${index + 1} with full cleanup');
    notifyListeners();
  }

  /// Comprehensive cleanup of all state data associated with a specific LO index
  void _cleanupLoStateData(int index) {
    // Clean up edit controllers
    _loEditControllers.remove(index);
    _loEditStates.remove(index);

    // Clean up LO-specific dropdown selections
    _loFunctionTypes.remove(index);
    _loSelectedRoles.remove(index);
    _loSelectedMultipleRoles.remove(index);
    _loExecutionRights.remove(index);
    _loMultipleRoles.remove(index);
    _loMultipleExecutionRights.remove(index);

    // Clean up selected objects data
    _loSelectedObjects.remove(index);
    _loSelectedObjectAttributes.remove(index);
    _loSelectedObjectsList.remove(index);

    // Clean up pathway-related state
    _pathwaySelectedRoles.remove(index);
    _pathwaySelectedMultipleRoles.remove(index);
    _pathwaySelectedTypes.remove(index);
    _pathwaySelectedLOs.remove(index);
    _pathwayEntries.remove(index);
    _pathwayCreationStates.remove(index);
    _isPathwayCreationOpen.remove(index);

    // Clean up LO insertion state
    _isLoInsertionOpen.remove(index);
    _loInsertionControllers.remove(index);
    _loInsertionStates.remove(index);

    // Shift all higher indices down by 1 to maintain consistency
    _shiftIndicesDown(index);

    Logger.info(
        'GoDetailsProvider: Cleaned up all state data for LO-${index + 1}');
  }

  /// Shift all indices greater than the removed index down by 1
  void _shiftIndicesDown(int removedIndex) {
    // Helper function to shift map entries
    void shiftMap<T>(Map<int, T> map) {
      final keysToShift = map.keys.where((key) => key > removedIndex).toList();
      for (final key in keysToShift) {
        final value = map.remove(key);
        if (value != null) {
          map[key - 1] = value;
        }
      }
    }

    // Shift all relevant maps
    shiftMap(_loEditControllers);
    shiftMap(_loEditStates);
    shiftMap(_loFunctionTypes);
    shiftMap(_loSelectedRoles);
    shiftMap(_loSelectedMultipleRoles);
    shiftMap(_loExecutionRights);
    shiftMap(_loMultipleRoles);
    shiftMap(_loMultipleExecutionRights);
    shiftMap(_loSelectedObjects);
    shiftMap(_loSelectedObjectAttributes);
    shiftMap(_loSelectedObjectsList);
    shiftMap(_pathwaySelectedRoles);
    shiftMap(_pathwaySelectedMultipleRoles);
    shiftMap(_pathwaySelectedTypes);
    shiftMap(_pathwaySelectedLOs);
    shiftMap(_pathwayEntries);
    shiftMap(_pathwayCreationStates);
    shiftMap(_isPathwayCreationOpen);
    shiftMap(_isLoInsertionOpen);
    shiftMap(_loInsertionControllers);
    shiftMap(_loInsertionStates);

    Logger.info(
        'GoDetailsProvider: Shifted all indices down after removing LO-${removedIndex + 1}');
  }

  /// Update LO numbers to maintain sequential numbering
  void _updateLoNumbers() {
    if (currentGoModel?.localObjectivesList == null) return;

    for (int i = 0; i < currentGoModel!.localObjectivesList!.length; i++) {
      currentGoModel!.localObjectivesList![i].loNumber = i + 1;
    }
  }

  /// Resets to initial step
  void resetToInitial() {
    _currentStep = GoDetailsStep.initial;
    _generatedDescription = null;
    // _localObjectives.clear();
    currentGoModel = null;
    descriptionController.clear();
    localObjectiveController.clear();
    _setValidationError(null);
    hideLocalObjectiveDetails();

    // Reset footer visibility when going back to initial step
    showFooter = false;

    Logger.info('GoDetailsProvider: Reset to initial step');
    notifyListeners();
  }

  /// Clears all form data
  void clearForm() {
    solutionController.clear();
    descriptionController.clear();
    localObjectiveController.clear();
    _selectedRole = null;
    hideLocalObjectiveDetails();
    resetToInitial();
    Logger.info('GoDetailsProvider: Form cleared');
  }

  /// Private helper methods
  void _setValidating(bool validating) {
    if (_isValidating != validating) {
      _isValidating = validating;
      notifyListeners();
    }
  }

  void _setValidationError(String? error) {
    if (_validationError != error) {
      _validationError = error;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    // Remove listeners before disposing controllers
    solutionController.removeListener(_onSolutionChanged);
    descriptionController.removeListener(_onDescriptionChanged);

    solutionController.dispose();
    descriptionController.dispose();
    localObjectiveController.dispose();

    // Dispose LO insertion controllers
    for (final controller in _loInsertionControllers.values) {
      controller.dispose();
    }
    _loInsertionControllers.clear();

    super.dispose();
  }

  /// Creates GoModel from current controller values
  GoModel createGoModelFromControllers(GoDetailsProvider goDetailsProvider) {
    // Create GlobalObjectives with solution name and description
    final globalObjectives = GlobalObjectives(
      name: goDetailsProvider.solutionController.text.trim(),
      description: goDetailsProvider.descriptionController.text.trim(),
      naturalLanguage: goDetailsProvider.solutionController.text.trim(),
      roleTypes: goDetailsProvider.selectedRoles
          .map((role) => role.name ?? '')
          .where((name) => name.isNotEmpty)
          .toList(),
      version: "1.0",
      status: "Active",
      classification: "",
      isValidated: false,
      isPublished: false,
    );

    // Create LocalObjectivesList from LO names
    final localObjectivesList = <LocalObjectivesList>[];
    if (currentGoModel != null) {
      for (int i = 0; i < currentGoModel!.localObjectivesList!.length; i++) {
        final loName = currentGoModel!.localObjectivesList![i];
        if ((loName.name ?? '').isNotEmpty) {
          // Create pathway data if it exists (either currently open or previously saved)
          PathwayData? pathwayData;

          // First check if there's saved pathway data in the current GoModel
          if (loName.pathwayData != null) {
            pathwayData = loName.pathwayData;
            Logger.info(
                'GoDetailsProvider: Using saved pathway data for LO-${i + 1}');
          }
          // If no saved data but pathway creation is currently open, create from current state
          else if (isPathwayCreationOpen(i)) {
            pathwayData = _createPathwayData(i, goDetailsProvider);
            Logger.info(
                'GoDetailsProvider: Creating pathway data from current state for LO-${i + 1}');
          }

          // Get role types from pathway data or current selection
          final roleTypes = pathwayData?.selectedRoles ??
              getPathwaySelectedMultipleRoles(i)
                  .map((role) => role.name ?? '')
                  .where((name) => name.isNotEmpty)
                  .toList();

          // Get terminal status from pathway data or current selection
          final isTerminal = pathwayData?.isTerminal ??
              (getPathwaySelectedType(i) == 'Terminal');

          localObjectivesList.add(LocalObjectivesList(
            loNumber: i + 1,
            name: loName.name,
            version: "1.0",
            status: "Active",
            naturalLanguage: loName.name,
            goId: "go_${DateTime.now().millisecondsSinceEpoch}",
            loId: "lo_${i + 1}_${DateTime.now().millisecondsSinceEpoch}",
            roleTypes: roleTypes,
            terminal: isTerminal,
            pathwayData: pathwayData,
          ));
        }
      }
    }

    // Create and return GoModel
    return GoModel(
      globalObjectives: globalObjectives,
      localObjectivesList: localObjectivesList,
    );
  }

  /// Creates PathwayData from provider data for a specific LO
  PathwayData _createPathwayData(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final selectedType = goDetailsProvider.getPathwaySelectedType(loIndex);
    final selectedRoles =
        goDetailsProvider.getPathwaySelectedMultipleRoles(loIndex);

    PathwayData pathwayData = PathwayData(
      selectedRoles: selectedRoles
          .map((role) => role.name ?? '')
          .where((name) => name.isNotEmpty)
          .toList(),
      selectedType: selectedType,
    );

    switch (selectedType) {
      case 'Sequential':
        final selectedLO = goDetailsProvider.getPathwaySelectedLO(loIndex);
        pathwayData.sequentialData = SequentialPathwayData(
          selectedLO: selectedLO,
        );
        break;

      case 'Alternative':
        // Get all pathway entries for this LO
        final pathwayEntries = goDetailsProvider.getPathwayEntries(loIndex);
        pathwayData.alternativeData = AlternativePathwayData(
          pathwayEntries: pathwayEntries,
        );
        break;

      case 'Parallel':
        // Get all pathway entries for this LO
        final pathwayEntries = goDetailsProvider.getPathwayEntries(loIndex);
        pathwayData.parallelData = ParallelPathwayData(
          pathwayEntries: pathwayEntries,
        );
        break;

      case 'Recursive':
        pathwayData.recursiveData = RecursivePathwayData(
          isRecursive: true,
        );
        break;

      case 'Terminal':
        pathwayData.isTerminal = true;
        break;
    }

    return pathwayData;
  }

  Future<bool> validateGO({bool isEditMode = false}) async {
    Logger.info('GoDetailsProvider: validateGO() called');

    // Ensure all pathway data is saved before validation
    await saveAllPathwayDataBeforeValidation();

    // Add your validation logic here
    // For example, you might want to validate the current GoModel
    if (currentGoModel != null) {
      Logger.info(
          'GoDetailsProvider: Validating GO: ${currentGoModel!.globalObjectives?.name}');

      try {
        final authService = AuthService();
        final savedData = await authService.getSavedAuthData();
        final tenantId = savedData.data?.user?.tenantId ?? '';
        final tenantName = savedData.data?.user?.tenantName ?? '';
        currentGoModel = createGoModelFromControllers(this);

        // Log which LOs are being included in validation
        Logger.info(
            'GoDetailsProvider: GO validation will include ${currentGoModel!.localObjectivesList?.length ?? 0} LOs:');
        for (int i = 0;
            i < (currentGoModel!.localObjectivesList?.length ?? 0);
            i++) {
          final lo = currentGoModel!.localObjectivesList![i];
          final hasPathway = lo.pathwayData != null;
          Logger.info('  - LO-${i + 1}: ${lo.name} (pathway: $hasPathway)');
        }

        String naturalLanguage =
            GoModelToTextConverter.convertToText(currentGoModel!, tenantId);
        Logger.info('🔥 GoText: $naturalLanguage');
        final result = await _entityParseValidationService.parseValidateGO(
          naturalLanguage: naturalLanguage,
        );

        parseValidateGo = result;

        if (parseValidateGo?.success ?? false) {
          final result1 =
              await _entityParseValidationService.parseValidateSaveGO(
                  naturalLanguage: naturalLanguage, isEditMode: isEditMode);
          parseValidateGo = result1;
          if (parseValidateGo?.success ?? false) {
            currentGoModel?.globalObjectives?.goId =
                parseValidateGo?.parsedData?.go?.globalObjectives?.goId;
            isGoValidateVisible = true;
            currentGoModel?.globalObjectives?.isValidated = true;
            notifyListeners();
            return false;
          } else {
            return true;
          }
        } else {
          return true; // Has error
        }
      } catch (e, s) {
        if (kDebugMode) {
          Logger.info("$e. $s");
        }
        Logger.error('GoDetailsProvider: Error validating GO: $e');
        return true; // Has error
      }
    } else {
      Logger.warning('GoDetailsProvider: No GoModel to validate');
      return true; // Has error
    }
  }

  Future<bool> publishGo({bool isEditMode = false}) async {
    Logger.info('GoDetailsProvider: publishGo() called');
    if (currentGoModel != null) {
      Logger.info(
          'GoDetailsProvider: Publishing GO: ${currentGoModel!.globalObjectives?.name}');

      try {
        final authService = AuthService();
        final savedData = await authService.getSavedAuthData();
        final tenantId = savedData.data?.user?.tenantId ?? '';
        String naturalLanguage =
            GoModelToTextConverter.convertToText(currentGoModel!, tenantId);
        Logger.info('🔥 GoText: $naturalLanguage');
        final result = await _entityParseValidationService.publishGo(
          naturalLanguage: naturalLanguage,
        );

        parseValidateGo = result;

        if (parseValidateGo?.success ?? false) {
          // final result1 =
          //     await _entityParseValidationService.parseValidateSaveGO(
          //         naturalLanguage: naturalLanguage, isEditMode: isEditMode);
          //  parseValidateGo = result1;
          if (parseValidateGo?.success ?? false) {
            currentGoModel?.globalObjectives?.goId =
                parseValidateGo?.parsedData?.go?.globalObjectives?.goId;
            isGoValidateVisible = true;
            currentGoModel?.globalObjectives?.isPublished = true;
            notifyListeners();
            return false; // No error
          } else {
            return true; // Has error
          }
        } else {
          return true; // Has error
        }
      } catch (e, s) {
        if (kDebugMode) {
          Logger.info("$e. $s");
        }
        Logger.error('GoDetailsProvider: Error publishing GO: $e');
        return true; // Has error
      }
    } else {
      Logger.warning('GoDetailsProvider: No GoModel to publish');
      return true; // Has error
    }
  }

  List<String> generatedPathways() {
    if ((currentGoModel?.localObjectivesList ?? []).isNotEmpty) {
      List<PathwayData> pathWayData = [];
      for (int i = 0;
          i < (currentGoModel?.localObjectivesList ?? []).length;
          i++) {
        currentGoModel?.localObjectivesList![i].pathwayData =
            _createPathwayData(i, this);
      }

      // Generate all possible pathway combinations
      return _generateAllPossiblePathways(
          currentGoModel?.localObjectivesList ?? []);
    } else {
      return [];
    }
  }

  /// Generate all possible pathway combinations considering alternatives
  List<String> _generateAllPossiblePathways(
      List<LocalObjectivesList> localObjectivesList) {
    if (localObjectivesList.isEmpty) return [];

    // Find all pathways by recursively building combinations
    List<List<String>> allPaths =
        _buildAllPathwayCombinations(localObjectivesList, 0, []);

    // Convert to formatted strings
    List<String> formattedPaths = [];
    for (int i = 0; i < allPaths.length; i++) {
      if (allPaths[i].isNotEmpty) {
        formattedPaths.add('STEPS: ${allPaths[i].join(' → ')}');
      }
    }

    return formattedPaths;
  }

  /// Recursively build all possible pathway combinations
  List<List<String>> _buildAllPathwayCombinations(
      List<LocalObjectivesList> localObjectivesList,
      int currentLoIndex,
      List<String> currentPath) {
    List<List<String>> allPaths = [];

    // Base case: if we've processed all LOs, return the current path
    if (currentLoIndex >= localObjectivesList.length) {
      return [List.from(currentPath)];
    }

    LocalObjectivesList currentLo = localObjectivesList[currentLoIndex];
    String currentLoNumber = 'LO-${currentLo.loNumber ?? (currentLoIndex + 1)}';

    // Add current LO to path
    List<String> newPath = List.from(currentPath);
    newPath.add(currentLoNumber);

    // Check if this LO has pathway data
    if (currentLo.pathwayData != null) {
      String? pathwayType = currentLo.pathwayData!.selectedType;

      switch (pathwayType) {
        case 'Alternative':
          // Handle alternative pathways - create separate paths for each alternative
          if (currentLo.pathwayData!.alternativeData != null) {
            var alternativeEntries =
                currentLo.pathwayData!.alternativeData!.pathwayEntries;

            if (alternativeEntries.isNotEmpty) {
              // Group alternatives by their selectedLO
              Map<String?, List<PathwayEntry>> groupedAlternatives = {};

              for (var entry in alternativeEntries) {
                String? targetLo = entry.selectedLO;
                if (targetLo != null && targetLo.isNotEmpty) {
                  if (!groupedAlternatives.containsKey(targetLo)) {
                    groupedAlternatives[targetLo] = [];
                  }
                  groupedAlternatives[targetLo]!.add(entry);
                }
              }

              // Create separate paths for each alternative target
              for (String? targetLo in groupedAlternatives.keys) {
                if (targetLo != null) {
                  // Find the index of the target LO
                  int? targetLoIndex =
                      _findLoIndexByName(localObjectivesList, targetLo);

                  if (targetLoIndex != null &&
                      targetLoIndex < localObjectivesList.length) {
                    // Create path through this alternative
                    List<String> alternativePath = List.from(newPath);

                    // Add target LO
                    String targetLoNumber =
                        'LO-${localObjectivesList[targetLoIndex].loNumber ?? (targetLoIndex + 1)}';
                    alternativePath.add(targetLoNumber);

                    // Continue building path from the target LO's next position
                    List<List<String>> continuedPaths =
                        _buildAllPathwayCombinations(localObjectivesList,
                            targetLoIndex + 1, alternativePath);

                    allPaths.addAll(continuedPaths);
                  }
                }
              }

              // If no valid alternatives found, continue with next LO
              if (allPaths.isEmpty) {
                List<List<String>> continuedPaths =
                    _buildAllPathwayCombinations(
                        localObjectivesList, currentLoIndex + 1, newPath);
                allPaths.addAll(continuedPaths);
              }
            } else {
              // No alternative entries, continue with next LO
              List<List<String>> continuedPaths = _buildAllPathwayCombinations(
                  localObjectivesList, currentLoIndex + 1, newPath);
              allPaths.addAll(continuedPaths);
            }
          } else {
            // No alternative data, continue with next LO
            // List<List<String>> continuedPaths = _buildAllPathwayCombinations(
            //     localObjectivesList, currentLoIndex + 1, newPath);
            // allPaths.addAll(continuedPaths);
          }
          break;

        case 'Sequential':
          // Handle sequential pathways
          if (currentLo.pathwayData!.sequentialData?.selectedLO != null) {
            String targetLo =
                currentLo.pathwayData!.sequentialData!.selectedLO!;
            int? targetLoIndex =
                _findLoIndexByName(localObjectivesList, targetLo);

            if (targetLoIndex != null &&
                targetLoIndex < localObjectivesList.length) {
              // Jump to the target LO
              List<List<String>> continuedPaths = _buildAllPathwayCombinations(
                  localObjectivesList, targetLoIndex, newPath);
              allPaths.addAll(continuedPaths);
            } else {
              // Continue with next LO
              List<List<String>> continuedPaths = _buildAllPathwayCombinations(
                  localObjectivesList, currentLoIndex + 1, newPath);
              allPaths.addAll(continuedPaths);
            }
          } else {
            // // Continue with next LO
            // List<List<String>> continuedPaths = _buildAllPathwayCombinations(
            //     localObjectivesList, currentLoIndex + 1, newPath);
            // allPaths.addAll(continuedPaths);
          }
          break;

        case 'Terminal':
          // Terminal LO - end the path here
          return [newPath];

        case 'Parallel':
          if (currentLo.pathwayData!.parallelData != null) {
            var parallelEntries =
                currentLo.pathwayData!.parallelData!.pathwayEntries;

            if (parallelEntries.isNotEmpty) {
              // Group alternatives by their selectedLO
              Map<String?, List<PathwayEntry>> groupedAlternatives = {};

              for (var entry in parallelEntries) {
                String? targetLo = entry.selectedLO;
                if (targetLo != null && targetLo.isNotEmpty) {
                  if (!groupedAlternatives.containsKey(targetLo)) {
                    groupedAlternatives[targetLo] = [];
                  }
                  groupedAlternatives[targetLo]!.add(entry);
                }
              }

              // Create separate paths for each alternative target
              for (String? targetLo in groupedAlternatives.keys) {
                if (targetLo != null) {
                  // Find the index of the target LO
                  int? targetLoIndex =
                      _findLoIndexByName(localObjectivesList, targetLo);

                  if (targetLoIndex != null &&
                      targetLoIndex < localObjectivesList.length) {
                    // Create path through this alternative
                    List<String> alternativePath = List.from(newPath);

                    // Add target LO
                    String targetLoNumber =
                        'LO-${localObjectivesList[targetLoIndex].loNumber ?? (targetLoIndex + 1)}';
                    alternativePath.add(targetLoNumber);

                    // Continue building path from the target LO's next position
                    List<List<String>> continuedPaths =
                        _buildAllPathwayCombinations(localObjectivesList,
                            targetLoIndex + 1, alternativePath);

                    allPaths.addAll(continuedPaths);
                  }
                }
              }

              // If no valid alternatives found, continue with next LO
              if (allPaths.isEmpty) {
                List<List<String>> continuedPaths =
                    _buildAllPathwayCombinations(
                        localObjectivesList, currentLoIndex + 1, newPath);
                allPaths.addAll(continuedPaths);
              }
            } else {
              // No alternative entries, continue with next LO
              List<List<String>> continuedPaths = _buildAllPathwayCombinations(
                  localObjectivesList, currentLoIndex + 1, newPath);
              allPaths.addAll(continuedPaths);
            }
          } else {
            // No alternative data, continue with next LO
            // List<List<String>> continuedPaths = _buildAllPathwayCombinations(
            //     localObjectivesList, currentLoIndex + 1, newPath);
            // allPaths.addAll(continuedPaths);
          }

        case 'Recursive':
        default:
          // Default: continue with next LO
          // List<List<String>> continuedPaths = _buildAllPathwayCombinations(
          //     localObjectivesList, currentLoIndex + 1, newPath);
          // allPaths.addAll(continuedPaths);
          break;
      }
    } else {
      // No pathway data, continue with next LO
      // List<List<String>> continuedPaths = _buildAllPathwayCombinations(
      //     localObjectivesList, currentLoIndex + 1, newPath);
      // allPaths.addAll(continuedPaths);
    }

    return allPaths;
  }

  /// Find the index of a LO by its name or number
  int? _findLoIndexByName(
      List<LocalObjectivesList> localObjectivesList, String loName) {
    // First try to find by exact name match
    for (int i = 0; i < localObjectivesList.length; i++) {
      if (localObjectivesList[i].name == loName) {
        return i;
      }
    }

    // Try to find by LO number format (e.g., "LO-2" -> find LO with loNumber 2)
    if (loName.startsWith('LO-')) {
      String numberPart = loName.substring(3);
      try {
        int loNumber = int.parse(numberPart);
        for (int i = 0; i < localObjectivesList.length; i++) {
          if (localObjectivesList[i].loNumber == loNumber) {
            return i;
          }
        }
      } catch (e) {
        // Invalid number format
      }
    }

    return null;
  }

  void removeLoInsertion(int index) {
    removeLocalObjective(index);
  }

  saveSolutionArtifact() async {
    final result = await localObjectiveValidationService.artifactSave(
        artifactType: "go",
        data: currentGoModel?.toJson(),
        artifactId: savedArtifactId);
    if (result != null) {
      savedArtifactId = result.artifactId;
    }
    // notifyListeners();
  }

  List<String> getAttributeListBasedOnLo(int loIndex, int entryIndex) {
    List<String> attributeList = [];
    if (_pathwayEntries[loIndex] != null &&
        _pathwayEntries[loIndex]![entryIndex].selectedLO != null) {
      if (currentGoModel?.localObjectivesList != null &&
          (currentGoModel?.localObjectivesList ?? []).isNotEmpty) {
        for (int i = 0; i < currentGoModel!.localObjectivesList!.length; i++) {
          //if(currentGoModel!.localObjectivesList![i].name == _pathwayEntries[loIndex]![entryIndex].selectedLO!){
          if (i == loIndex) {
            List<ObjectCreationModel> entityList =
                currentGoModel!.localObjectivesList![i].entitiesList ?? [];

            for (ObjectCreationModel objectCreationModel in entityList) {
              for (ObjectAttribute attribute
                  in objectCreationModel.attributes ?? []) {
                String entityName = objectCreationModel.displayName ?? "";
                entityName += " - ${attribute.displayName ?? ""}";
                // EntityAttributeDetails entityAttributeDetails = EntityAttributeDetails();
                // entityAttributeDetails.entityName = objectCreationModel.name;
                // entityAttributeDetails.entityId = objectCreationModel.id;
                // entityAttributeDetails.attributeName = attribute.name;
                // entityAttributeDetails.attributeId = attribute.id;
                // entityAttributeDetails.displayName = "${objectCreationModel.displayName} - ${attribute.displayName}";
                attributeList.add(entityName);
              }
            }
          }
        }
      }

      return attributeList;
    } else {
      return [];
    }
  }
}
