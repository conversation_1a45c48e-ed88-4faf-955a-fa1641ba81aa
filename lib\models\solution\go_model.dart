import '../object_creation_model.dart';

class GoModel {
  GlobalObjectives? globalObjectives;
  ProcessOwnership? processOwnership;
  TriggerDefinition? triggerDefinition;
  List<LocalObjectivesList>? localObjectivesList;
  List<PathwayDefinition>? pathwayDefinitions;
  PerformanceMetadataClass? performanceMetadata;
  ProcessMiningSchema? processMiningSchema;
  PerformanceMetadataClass? performanceDiscoveryMetrics;
  GoModelConformanceAnalytics? conformanceAnalytics;
  GoModelAdvancedProcessIntelligence? advancedProcessIntelligence;
  List<RollbackPathway>? rollbackPathways;
  PerformanceMetadataClass? validationRules;
  List<DataConstraint>? dataConstraints;
  List<ProcessFlow>? processFlow;

  GoModel({
    this.globalObjectives,
    this.processOwnership,
    this.triggerDefinition,
    this.localObjectivesList,
    this.pathwayDefinitions,
    this.performanceMetadata,
    this.processMiningSchema,
    this.performanceDiscoveryMetrics,
    this.conformanceAnalytics,
    this.advancedProcessIntelligence,
    this.rollbackPathways,
    this.validationRules,
    this.dataConstraints,
    this.processFlow,
  });

  factory GoModel.fromJson(Map<String, dynamic> json) {
    return GoModel(
      globalObjectives: json['globalObjectives'] != null
          ? GlobalObjectives.fromJson(json['globalObjectives'])
          : null,
      processOwnership: json['processOwnership'] != null
          ? ProcessOwnership.fromJson(json['processOwnership'])
          : null,
      triggerDefinition: json['triggerDefinition'] != null
          ? TriggerDefinition.fromJson(json['triggerDefinition'])
          : null,
      localObjectivesList: json['localObjectivesList'] != null
          ? (json['localObjectivesList'] as List)
              .map((item) => LocalObjectivesList.fromJson(item))
              .toList()
          : null,
      pathwayDefinitions: json['pathwayDefinitions'] != null
          ? (json['pathwayDefinitions'] as List)
              .map((item) => PathwayDefinition.fromJson(item))
              .toList()
          : null,
      performanceMetadata: json['performanceMetadata'] != null
          ? PerformanceMetadataClass.fromJson(json['performanceMetadata'])
          : null,
      processMiningSchema: json['processMiningSchema'] != null
          ? ProcessMiningSchema.fromJson(json['processMiningSchema'])
          : null,
      performanceDiscoveryMetrics: json['performanceDiscoveryMetrics'] != null
          ? PerformanceMetadataClass.fromJson(
              json['performanceDiscoveryMetrics'])
          : null,
      conformanceAnalytics: json['conformanceAnalytics'] != null
          ? GoModelConformanceAnalytics.fromJson(json['conformanceAnalytics'])
          : null,
      advancedProcessIntelligence: json['advancedProcessIntelligence'] != null
          ? GoModelAdvancedProcessIntelligence.fromJson(
              json['advancedProcessIntelligence'])
          : null,
      rollbackPathways: json['rollbackPathways'] != null
          ? (json['rollbackPathways'] as List)
              .map((item) => RollbackPathway.fromJson(item))
              .toList()
          : null,
      validationRules: json['validationRules'] != null
          ? PerformanceMetadataClass.fromJson(json['validationRules'])
          : null,
      dataConstraints: json['dataConstraints'] != null
          ? (json['dataConstraints'] as List)
              .map((item) => DataConstraint.fromJson(item))
              .toList()
          : null,
      processFlow: json['processFlow'] != null
          ? (json['processFlow'] as List)
              .map((item) => ProcessFlow.fromJson(item))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'globalObjectives': globalObjectives?.toJson(),
      'processOwnership': processOwnership?.toJson(),
      'triggerDefinition': triggerDefinition?.toJson(),
      'localObjectivesList':
          localObjectivesList?.map((item) => item.toJson()).toList(),
      'pathwayDefinitions':
          pathwayDefinitions?.map((item) => item.toJson()).toList(),
      'performanceMetadata': performanceMetadata?.toJson(),
      'processMiningSchema': processMiningSchema?.toJson(),
      'performanceDiscoveryMetrics': performanceDiscoveryMetrics?.toJson(),
      'conformanceAnalytics': conformanceAnalytics?.toJson(),
      'advancedProcessIntelligence': advancedProcessIntelligence?.toJson(),
      'rollbackPathways':
          rollbackPathways?.map((item) => item.toJson()).toList(),
      'validationRules': validationRules?.toJson(),
      'dataConstraints': dataConstraints?.map((item) => item.toJson()).toList(),
      'processFlow': processFlow?.map((item) => item.toJson()).toList(),
    };
  }
}

class GoModelAdvancedProcessIntelligence {
  String? id;
  String? goId;
  String? naturalLanguage;
  ProcessHealthScore? processHealthScore;
  PredictionModels? predictionModels;
  OptimizationInsights? optimizationInsights;

  GoModelAdvancedProcessIntelligence({
    this.id,
    this.goId,
    this.naturalLanguage,
    this.processHealthScore,
    this.predictionModels,
    this.optimizationInsights,
  });

  factory GoModelAdvancedProcessIntelligence.fromJson(
      Map<String, dynamic> json) {
    return GoModelAdvancedProcessIntelligence(
      id: json['id'],
      goId: json['goId'],
      naturalLanguage: json['naturalLanguage'],
      processHealthScore: json['processHealthScore'] != null
          ? ProcessHealthScore.fromJson(json['processHealthScore'])
          : null,
      predictionModels: json['predictionModels'] != null
          ? PredictionModels.fromJson(json['predictionModels'])
          : null,
      optimizationInsights: json['optimizationInsights'] != null
          ? OptimizationInsights.fromJson(json['optimizationInsights'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'goId': goId,
      'naturalLanguage': naturalLanguage,
      'processHealthScore': processHealthScore?.toJson(),
      'predictionModels': predictionModels?.toJson(),
      'optimizationInsights': optimizationInsights?.toJson(),
    };
  }
}

class OptimizationInsights {
  List<String>? bottleneckElimination;
  List<String>? resourceReallocation;
  List<String>? pathwayOptimization;

  OptimizationInsights({
    this.bottleneckElimination,
    this.resourceReallocation,
    this.pathwayOptimization,
  });

  factory OptimizationInsights.fromJson(Map<String, dynamic> json) {
    return OptimizationInsights(
      bottleneckElimination: json['bottleneckElimination'] != null
          ? List<String>.from(json['bottleneckElimination'])
          : null,
      resourceReallocation: json['resourceReallocation'] != null
          ? List<String>.from(json['resourceReallocation'])
          : null,
      pathwayOptimization: json['pathwayOptimization'] != null
          ? List<String>.from(json['pathwayOptimization'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'bottleneckElimination': bottleneckElimination,
      'resourceReallocation': resourceReallocation,
      'pathwayOptimization': pathwayOptimization,
    };
  }
}

class PredictionModels {
  CompletionTimeForecast? completionTimeForecast;

  PredictionModels({
    this.completionTimeForecast,
  });

  factory PredictionModels.fromJson(Map<String, dynamic> json) {
    return PredictionModels(
      completionTimeForecast: json['completionTimeForecast'] != null
          ? CompletionTimeForecast.fromJson(json['completionTimeForecast'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'completionTimeForecast': completionTimeForecast?.toJson(),
    };
  }
}

class CompletionTimeForecast {
  String? algorithm;
  int? accuracy;
  String? confidenceInterval;

  CompletionTimeForecast({
    this.algorithm,
    this.accuracy,
    this.confidenceInterval,
  });

  factory CompletionTimeForecast.fromJson(Map<String, dynamic> json) {
    return CompletionTimeForecast(
      algorithm: json['algorithm'],
      accuracy: json['accuracy'],
      confidenceInterval: json['confidenceInterval'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'algorithm': algorithm,
      'accuracy': accuracy,
      'confidenceInterval': confidenceInterval,
    };
  }
}

class ProcessHealthScore {
  int? performanceScore;
  int? complianceScore;
  int? efficiencyScore;
  int? overallHealth;

  ProcessHealthScore({
    this.performanceScore,
    this.complianceScore,
    this.efficiencyScore,
    this.overallHealth,
  });

  factory ProcessHealthScore.fromJson(Map<String, dynamic> json) {
    return ProcessHealthScore(
      performanceScore: json['performanceScore'],
      complianceScore: json['complianceScore'],
      efficiencyScore: json['efficiencyScore'],
      overallHealth: json['overallHealth'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'performanceScore': performanceScore,
      'complianceScore': complianceScore,
      'efficiencyScore': efficiencyScore,
      'overallHealth': overallHealth,
    };
  }
}

class GoModelConformanceAnalytics {
  String? id;
  String? goId;
  String? naturalLanguage;
  int? complianceRate;
  PurpleExceptionPatterns? exceptionPatterns;

  GoModelConformanceAnalytics({
    this.id,
    this.goId,
    this.naturalLanguage,
    this.complianceRate,
    this.exceptionPatterns,
  });

  factory GoModelConformanceAnalytics.fromJson(Map<String, dynamic> json) {
    return GoModelConformanceAnalytics(
      id: json['id'],
      goId: json['goId'],
      naturalLanguage: json['naturalLanguage'],
      complianceRate: json['complianceRate'],
      exceptionPatterns: json['exceptionPatterns'] != null
          ? PurpleExceptionPatterns.fromJson(json['exceptionPatterns'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'goId': goId,
      'naturalLanguage': naturalLanguage,
      'complianceRate': complianceRate,
      'exceptionPatterns': exceptionPatterns?.toJson(),
    };
  }
}

class PurpleExceptionPatterns {
  PurpleValidationFailure? validationFailure;
  SystemError? systemError;

  PurpleExceptionPatterns({
    this.validationFailure,
    this.systemError,
  });

  factory PurpleExceptionPatterns.fromJson(Map<String, dynamic> json) {
    return PurpleExceptionPatterns(
      validationFailure: json['validationFailure'] != null
          ? PurpleValidationFailure.fromJson(json['validationFailure'])
          : null,
      systemError: json['systemError'] != null
          ? SystemError.fromJson(json['systemError'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'validationFailure': validationFailure?.toJson(),
      'systemError': systemError?.toJson(),
    };
  }
}

class SystemError {
  int? frequency;
  List<String>? errorCategories;
  int? automaticRecoveryRate;

  SystemError({
    this.frequency,
    this.errorCategories,
    this.automaticRecoveryRate,
  });

  factory SystemError.fromJson(Map<String, dynamic> json) {
    return SystemError(
      frequency: json['frequency'],
      errorCategories: json['errorCategories'] != null
          ? List<String>.from(json['errorCategories'])
          : null,
      automaticRecoveryRate: json['automaticRecoveryRate'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'frequency': frequency,
      'errorCategories': errorCategories,
      'automaticRecoveryRate': automaticRecoveryRate,
    };
  }
}

class PurpleValidationFailure {
  int? frequency;
  List<String>? mostCommonFailures;
  String? resolutionTime;

  PurpleValidationFailure({
    this.frequency,
    this.mostCommonFailures,
    this.resolutionTime,
  });

  factory PurpleValidationFailure.fromJson(Map<String, dynamic> json) {
    return PurpleValidationFailure(
      frequency: json['frequency'],
      mostCommonFailures: json['mostCommonFailures'] != null
          ? List<String>.from(json['mostCommonFailures'])
          : null,
      resolutionTime: json['resolutionTime'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'frequency': frequency,
      'mostCommonFailures': mostCommonFailures,
      'resolutionTime': resolutionTime,
    };
  }
}

class DataConstraint {
  String? id;
  String? goId;
  String? entity;
  String? attribute;
  String? dataType;
  String? constraintType;
  String? constraintText;
  String? errorMessage;
  String? naturalLanguage;

  DataConstraint({
    this.id,
    this.goId,
    this.entity,
    this.attribute,
    this.dataType,
    this.constraintType,
    this.constraintText,
    this.errorMessage,
    this.naturalLanguage,
  });

  factory DataConstraint.fromJson(Map<String, dynamic> json) {
    return DataConstraint(
      id: json['id'],
      goId: json['goId'],
      entity: json['entity'],
      attribute: json['attribute'],
      dataType: json['dataType'],
      constraintType: json['constraintType'],
      constraintText: json['constraintText'],
      errorMessage: json['errorMessage'],
      naturalLanguage: json['naturalLanguage'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'goId': goId,
      'entity': entity,
      'attribute': attribute,
      'dataType': dataType,
      'constraintType': constraintType,
      'constraintText': constraintText,
      'errorMessage': errorMessage,
      'naturalLanguage': naturalLanguage,
    };
  }
}

class GlobalObjectives {
  String? naturalLanguage;
  String? name;
  String? version;
  String? status;
  String? description;
  String? primaryEntity;
  String? classification;
  String? agentType;
  String? bookName;
  String? chapterName;
  String? tenantName;
  String? goId;
  String? tenantId;
  String? bookId;
  String? chapterId;
  String? versionType;
  List<String>?
      roleTypes; // Changed from String? roleType to support multiple roles
  List<String>?
      multipleExecutionRights; // Support multiple execution rights for multiple roles
  bool? isValidated;
  bool? isPublished;

  GlobalObjectives(
      {this.naturalLanguage,
      this.name,
      this.version,
      this.status,
      this.description,
      this.primaryEntity,
      this.classification,
      this.agentType,
      this.bookName,
      this.chapterName,
      this.tenantName,
      this.goId,
      this.tenantId,
      this.bookId,
      this.chapterId,
      this.versionType,
      this.roleTypes,
      this.multipleExecutionRights,
      this.isValidated,
      this.isPublished});

  factory GlobalObjectives.fromJson(Map<String, dynamic> json) {
    return GlobalObjectives(
      naturalLanguage: json['naturalLanguage'],
      name: json['name'],
      version: json['version'],
      status: json['status'],
      description: json['description'],
      primaryEntity: json['primaryEntity'],
      classification: json['classification'],
      agentType: json['agentType'],
      bookName: json['bookName'],
      chapterName: json['chapterName'],
      tenantName: json['tenantName'],
      goId: json['goId'],
      tenantId: json['tenantId'],
      bookId: json['bookId'],
      chapterId: json['chapterId'],
      versionType: json['versionType'],
      roleTypes: json['roleTypes'] != null
          ? List<String>.from(json['roleTypes'])
          : null,
      isValidated: json['isValidated'],
      isPublished: json['isPublished'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'naturalLanguage': naturalLanguage,
      'name': name,
      'version': version,
      'status': status,
      'description': description,
      'primaryEntity': primaryEntity,
      'classification': classification,
      'agentType': agentType,
      'bookName': bookName,
      'chapterName': chapterName,
      'tenantName': tenantName,
      'goId': goId,
      'tenantId': tenantId,
      'bookId': bookId,
      'chapterId': chapterId,
      'versionType': versionType,
      'roleTypes': roleTypes,
      'isValidated': isValidated,
      'isPublished': isPublished,
    };
  }
}

class LocalObjectivesList {
  int? loNumber;
  String? workSource;
  bool? terminal;
  String? name;
  String? version;
  String? status;
  String? workflowSource;
  String? functionType;
  String? agentType;
  String? executionRights;
  String? tenantName;
  String? tenantId;
  String? uiType;
  String? naturalLanguage;
  String? goId;
  String? loId;
  List<String>?
      roleTypes; // Changed from String? roleType to support multiple roles
  List<String>?
      multipleExecutionRights; // Support multiple execution rights for multiple roles
  bool? isValidated;
  bool? isPublished;
  bool? isLoValidateVisible;

  // Pathway-related fields
  PathwayData? pathwayData;

  List<ObjectCreationModel>? entitiesList;

  LocalObjectivesList({
    this.loNumber,
    this.workSource,
    this.terminal,
    this.name,
    this.version,
    this.status,
    this.workflowSource,
    this.functionType,
    this.agentType,
    this.executionRights,
    this.tenantName,
    this.tenantId,
    this.uiType,
    this.naturalLanguage,
    this.goId,
    this.loId,
    this.entitiesList,
    this.roleTypes,
    this.multipleExecutionRights,
    this.pathwayData,
    this.isValidated = false,
    this.isPublished = false,
    this.isLoValidateVisible = true,
  });

  factory LocalObjectivesList.fromJson(Map<String, dynamic> json) {
    return LocalObjectivesList(
      loNumber: json['loNumber'],
      workSource: json['workSource'],
      terminal: json['terminal'],
      name: json['name'],
      version: json['version'],
      status: json['status'],
      workflowSource: json['workflowSource'],
      functionType: json['functionType'],
      agentType: json['agentType'],
      executionRights: json['executionRights'],
      tenantName: json['tenantName'],
      tenantId: json['tenantId'],
      uiType: json['uiType'],
      naturalLanguage: json['naturalLanguage'],
      goId: json['goId'],
      loId: json['loId'],
      roleTypes: json['roleTypes'] != null
          ? List<String>.from(json['roleTypes'])
          : null,
      multipleExecutionRights: json['multipleExecutionRights'] != null
          ? List<String>.from(json['multipleExecutionRights'])
          : null,
      isValidated: json['isValidated'] ?? false,
      isPublished: json['isPublished'] ?? false,
      isLoValidateVisible: json['isLoValidateVisible'] ?? true,
      pathwayData: json['pathwayData'] != null
          ? PathwayData.fromJson(json['pathwayData'])
          : null,
      entitiesList: json['entitiesList'] != null
          ? (json['entitiesList'] as List)
              .map((item) => ObjectCreationModel.fromJson(item))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'loNumber': loNumber,
      'workSource': workSource,
      'terminal': terminal,
      'name': name,
      'version': version,
      'status': status,
      'workflowSource': workflowSource,
      'functionType': functionType,
      'agentType': agentType,
      'executionRights': executionRights,
      'tenantName': tenantName,
      'tenantId': tenantId,
      'uiType': uiType,
      'naturalLanguage': naturalLanguage,
      'goId': goId,
      'loId': loId,
      'roleTypes': roleTypes,
      'multipleExecutionRights': multipleExecutionRights,
      'isValidated': isValidated,
      'isPublished': isPublished,
      'isLoValidateVisible': isLoValidateVisible,
      'pathwayData': pathwayData?.toJson(),
      'entitiesList': entitiesList?.map((item) => item.toJson()).toList(),
    };
  }
}

class PathwayData {
  List<String>?
      selectedRoles; // Changed from String? selectedRole to support multiple roles
  String? selectedType;

  // Sequential pathway data
  SequentialPathwayData? sequentialData;

  // Alternative pathway data
  AlternativePathwayData? alternativeData;

  // Parallel pathway data
  ParallelPathwayData? parallelData;

  // Recursive pathway data
  RecursivePathwayData? recursiveData;

  // Terminal pathway data
  bool? isTerminal;

  PathwayData({
    this.selectedRoles,
    this.selectedType,
    this.sequentialData,
    this.alternativeData,
    this.parallelData,
    this.recursiveData,
    this.isTerminal,
  });

  factory PathwayData.fromJson(Map<String, dynamic> json) {
    return PathwayData(
      selectedRoles: json['selectedRoles'] != null
          ? List<String>.from(json['selectedRoles'])
          : null,
      selectedType: json['selectedType'],
      sequentialData: json['sequentialData'] != null
          ? SequentialPathwayData.fromJson(json['sequentialData'])
          : null,
      alternativeData: json['alternativeData'] != null
          ? AlternativePathwayData.fromJson(json['alternativeData'])
          : null,
      parallelData: json['parallelData'] != null
          ? ParallelPathwayData.fromJson(json['parallelData'])
          : null,
      recursiveData: json['recursiveData'] != null
          ? RecursivePathwayData.fromJson(json['recursiveData'])
          : null,
      isTerminal: json['isTerminal'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'selectedRoles': selectedRoles,
      'selectedType': selectedType,
      'sequentialData': sequentialData?.toJson(),
      'alternativeData': alternativeData?.toJson(),
      'parallelData': parallelData?.toJson(),
      'recursiveData': recursiveData?.toJson(),
      'isTerminal': isTerminal,
    };
  }
}

class SequentialPathwayData {
  String? selectedLO;

  SequentialPathwayData({
    this.selectedLO,
  });

  factory SequentialPathwayData.fromJson(Map<String, dynamic> json) {
    return SequentialPathwayData(
      selectedLO: json['selectedLO'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'selectedLO': selectedLO,
    };
  }
}

class PathwayEntry {
  String? selectedLO;
  String? entityAttribute;
  String? condition;
  String? entityAttributeAfterCondition;

  PathwayEntry({
    this.selectedLO,
    this.entityAttribute,
    this.condition,
    this.entityAttributeAfterCondition,
  });

  Map<String, dynamic> toJson() {
    return {
      'selectedLO': selectedLO,
      'entityAttribute': entityAttribute,
      'condition': condition,
      'entityAttributeAfterCondition': entityAttributeAfterCondition,
    };
  }

  factory PathwayEntry.fromJson(Map<String, dynamic> json) {
    return PathwayEntry(
      selectedLO: json['selectedLO'],
      entityAttribute: json['entityAttribute'],
      condition: json['condition'],
      entityAttributeAfterCondition: json['entityAttributeAfterCondition'],
    );
  }
}

class AlternativePathwayData {
  List<PathwayEntry> pathwayEntries;

  AlternativePathwayData({
    List<PathwayEntry>? pathwayEntries,
  }) : pathwayEntries = pathwayEntries ?? [];

  Map<String, dynamic> toJson() {
    return {
      'pathwayEntries': pathwayEntries.map((entry) => entry.toJson()).toList(),
    };
  }

  factory AlternativePathwayData.fromJson(Map<String, dynamic> json) {
    return AlternativePathwayData(
      pathwayEntries: (json['pathwayEntries'] as List<dynamic>?)
              ?.map((entry) => PathwayEntry.fromJson(entry))
              .toList() ??
          [],
    );
  }
}

class ParallelPathwayData {
  List<PathwayEntry> pathwayEntries;

  ParallelPathwayData({
    List<PathwayEntry>? pathwayEntries,
  }) : pathwayEntries = pathwayEntries ?? [];

  Map<String, dynamic> toJson() {
    return {
      'pathwayEntries': pathwayEntries.map((entry) => entry.toJson()).toList(),
    };
  }

  factory ParallelPathwayData.fromJson(Map<String, dynamic> json) {
    return ParallelPathwayData(
      pathwayEntries: (json['pathwayEntries'] as List<dynamic>?)
              ?.map((entry) => PathwayEntry.fromJson(entry))
              .toList() ??
          [],
    );
  }
}

class RecursivePathwayData {
  bool? isRecursive;

  RecursivePathwayData({
    this.isRecursive,
  });

  factory RecursivePathwayData.fromJson(Map<String, dynamic> json) {
    return RecursivePathwayData(
      isRecursive: json['isRecursive'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isRecursive': isRecursive,
    };
  }
}

class PathwayDefinition {
  String? id;
  int? pathwayNumber;
  String? pathwayName;
  List<String>? steps;
  String? naturalLanguage;

  PathwayDefinition({
    this.id,
    this.pathwayNumber,
    this.pathwayName,
    this.steps,
    this.naturalLanguage,
  });

  factory PathwayDefinition.fromJson(Map<String, dynamic> json) {
    return PathwayDefinition(
      id: json['id'],
      pathwayNumber: json['pathwayNumber'],
      pathwayName: json['pathwayName'],
      steps: json['steps'] != null ? List<String>.from(json['steps']) : null,
      naturalLanguage: json['naturalLanguage'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'pathwayNumber': pathwayNumber,
      'pathwayName': pathwayName,
      'steps': steps,
      'naturalLanguage': naturalLanguage,
    };
  }
}

class PerformanceMetadataClass {
  String? id;
  String? goId;
  String? naturalLanguage;
  PerformanceMetadataResourcePatterns? resourcePatterns;
  MetadataData? metadataData;
  List<Rule>? rules;

  PerformanceMetadataClass({
    this.id,
    this.goId,
    this.naturalLanguage,
    this.resourcePatterns,
    this.metadataData,
    this.rules,
  });

  factory PerformanceMetadataClass.fromJson(Map<String, dynamic> json) {
    return PerformanceMetadataClass(
      id: json['id'],
      goId: json['goId'],
      naturalLanguage: json['naturalLanguage'],
      resourcePatterns: json['resourcePatterns'] != null
          ? PerformanceMetadataResourcePatterns.fromJson(
              json['resourcePatterns'])
          : null,
      metadataData: json['metadataData'] != null
          ? MetadataData.fromJson(json['metadataData'])
          : null,
      rules: json['rules'] != null
          ? (json['rules'] as List).map((item) => Rule.fromJson(item)).toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'goId': goId,
      'naturalLanguage': naturalLanguage,
      'resourcePatterns': resourcePatterns?.toJson(),
      'metadataData': metadataData?.toJson(),
      'rules': rules?.map((item) => item.toJson()).toList(),
    };
  }
}

class MetadataData {
  String? cycleTime;
  int? numberOfPathways;
  VolumeMetrics? volumeMetrics;
  SlaThresholds? slaThresholds;
  CriticalLoPerformance? criticalLoPerformance;

  MetadataData({
    this.cycleTime,
    this.numberOfPathways,
    this.volumeMetrics,
    this.slaThresholds,
    this.criticalLoPerformance,
  });

  factory MetadataData.fromJson(Map<String, dynamic> json) {
    return MetadataData(
      cycleTime: json['cycleTime'],
      numberOfPathways: json['numberOfPathways'],
      volumeMetrics: json['volumeMetrics'] != null
          ? VolumeMetrics.fromJson(json['volumeMetrics'])
          : null,
      slaThresholds: json['slaThresholds'] != null
          ? SlaThresholds.fromJson(json['slaThresholds'])
          : null,
      criticalLoPerformance: json['criticalLoPerformance'] != null
          ? CriticalLoPerformance.fromJson(json['criticalLoPerformance'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'cycleTime': cycleTime,
      'numberOfPathways': numberOfPathways,
      'volumeMetrics': volumeMetrics?.toJson(),
      'slaThresholds': slaThresholds?.toJson(),
      'criticalLoPerformance': criticalLoPerformance?.toJson(),
    };
  }
}

class CriticalLoPerformance {
  String? createEmployeeRecord;
  String? updateEmployeeRecord;
  String? viewEmployeeDatabase;
  String? deleteEmployeeRecord;

  CriticalLoPerformance({
    this.createEmployeeRecord,
    this.updateEmployeeRecord,
    this.viewEmployeeDatabase,
    this.deleteEmployeeRecord,
  });

  factory CriticalLoPerformance.fromJson(Map<String, dynamic> json) {
    return CriticalLoPerformance(
      createEmployeeRecord: json['createEmployeeRecord'],
      updateEmployeeRecord: json['updateEmployeeRecord'],
      viewEmployeeDatabase: json['viewEmployeeDatabase'],
      deleteEmployeeRecord: json['deleteEmployeeRecord'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'createEmployeeRecord': createEmployeeRecord,
      'updateEmployeeRecord': updateEmployeeRecord,
      'viewEmployeeDatabase': viewEmployeeDatabase,
      'deleteEmployeeRecord': deleteEmployeeRecord,
    };
  }
}

class SlaThresholds {
  String? recordCreation;
  String? recordUpdate;
  String? recordSearch;
  String? recordDeletion;

  SlaThresholds({
    this.recordCreation,
    this.recordUpdate,
    this.recordSearch,
    this.recordDeletion,
  });

  factory SlaThresholds.fromJson(Map<String, dynamic> json) {
    return SlaThresholds(
      recordCreation: json['recordCreation'],
      recordUpdate: json['recordUpdate'],
      recordSearch: json['recordSearch'],
      recordDeletion: json['recordDeletion'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'recordCreation': recordCreation,
      'recordUpdate': recordUpdate,
      'recordSearch': recordSearch,
      'recordDeletion': recordDeletion,
    };
  }
}

class VolumeMetrics {
  int? averageVolume;
  int? peakVolume;
  String? unit;

  VolumeMetrics({
    this.averageVolume,
    this.peakVolume,
    this.unit,
  });

  factory VolumeMetrics.fromJson(Map<String, dynamic> json) {
    return VolumeMetrics(
      averageVolume: json['averageVolume'],
      peakVolume: json['peakVolume'],
      unit: json['unit'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'averageVolume': averageVolume,
      'peakVolume': peakVolume,
      'unit': unit,
    };
  }
}

class PerformanceMetadataResourcePatterns {
  HrManager? hrManager;

  PerformanceMetadataResourcePatterns({
    this.hrManager,
  });

  factory PerformanceMetadataResourcePatterns.fromJson(
      Map<String, dynamic> json) {
    return PerformanceMetadataResourcePatterns(
      hrManager: json['hrManager'] != null
          ? HrManager.fromJson(json['hrManager'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hrManager': hrManager?.toJson(),
    };
  }
}

class HrManager {
  String? activeHours;
  String? peakLoadPeriods;
  int? concurrentExecutions;

  HrManager({
    this.activeHours,
    this.peakLoadPeriods,
    this.concurrentExecutions,
  });

  factory HrManager.fromJson(Map<String, dynamic> json) {
    return HrManager(
      activeHours: json['activeHours'],
      peakLoadPeriods: json['peakLoadPeriods'],
      concurrentExecutions: json['concurrentExecutions'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'activeHours': activeHours,
      'peakLoadPeriods': peakLoadPeriods,
      'concurrentExecutions': concurrentExecutions,
    };
  }
}

class Rule {
  String? id;
  String? ruleName;
  String? naturalLanguage;
  List<String>? ruleInputs;
  String? ruleOperation;
  String? ruleDescription;
  String? ruleOutput;
  String? ruleError;
  String? ruleValidation;

  Rule({
    this.id,
    this.ruleName,
    this.naturalLanguage,
    this.ruleInputs,
    this.ruleOperation,
    this.ruleDescription,
    this.ruleOutput,
    this.ruleError,
    this.ruleValidation,
  });

  factory Rule.fromJson(Map<String, dynamic> json) {
    return Rule(
      id: json['id'],
      ruleName: json['ruleName'],
      naturalLanguage: json['naturalLanguage'],
      ruleInputs: json['ruleInputs'] != null
          ? List<String>.from(json['ruleInputs'])
          : null,
      ruleOperation: json['ruleOperation'],
      ruleDescription: json['ruleDescription'],
      ruleOutput: json['ruleOutput'],
      ruleError: json['ruleError'],
      ruleValidation: json['ruleValidation'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ruleName': ruleName,
      'naturalLanguage': naturalLanguage,
      'ruleInputs': ruleInputs,
      'ruleOperation': ruleOperation,
      'ruleDescription': ruleDescription,
      'ruleOutput': ruleOutput,
      'ruleError': ruleError,
      'ruleValidation': ruleValidation,
    };
  }
}

class ProcessFlow {
  String? loName;
  String? actorType;
  String? description;
  String? routeType;
  String? id;
  String? goId;
  String? loId;
  String? naturalLanguage;
  List<String>? routes;

  ProcessFlow({
    this.loName,
    this.actorType,
    this.description,
    this.routeType,
    this.id,
    this.goId,
    this.loId,
    this.naturalLanguage,
    this.routes,
  });

  factory ProcessFlow.fromJson(Map<String, dynamic> json) {
    return ProcessFlow(
      loName: json['loName'],
      actorType: json['actorType'],
      description: json['description'],
      routeType: json['routeType'],
      id: json['id'],
      goId: json['goId'],
      loId: json['loId'],
      naturalLanguage: json['naturalLanguage'],
      routes: json['routes'] != null ? List<String>.from(json['routes']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'loName': loName,
      'actorType': actorType,
      'description': description,
      'routeType': routeType,
      'id': id,
      'goId': goId,
      'loId': loId,
      'naturalLanguage': naturalLanguage,
      'routes': routes,
    };
  }
}

class ProcessMiningSchema {
  String? naturalLanguage;
  SchemaData? schemaData;
  ProcessMiningSchemaPerformanceDiscoveryMetrics? performanceDiscoveryMetrics;
  ProcessMiningSchemaConformanceAnalytics? conformanceAnalytics;
  ProcessMiningSchemaAdvancedProcessIntelligence? advancedProcessIntelligence;
  String? id;
  String? goId;

  ProcessMiningSchema({
    this.naturalLanguage,
    this.schemaData,
    this.performanceDiscoveryMetrics,
    this.conformanceAnalytics,
    this.advancedProcessIntelligence,
    this.id,
    this.goId,
  });

  factory ProcessMiningSchema.fromJson(Map<String, dynamic> json) {
    return ProcessMiningSchema(
      naturalLanguage: json['naturalLanguage'],
      schemaData: json['schemaData'] != null
          ? SchemaData.fromJson(json['schemaData'])
          : null,
      performanceDiscoveryMetrics: json['performanceDiscoveryMetrics'] != null
          ? ProcessMiningSchemaPerformanceDiscoveryMetrics.fromJson(
              json['performanceDiscoveryMetrics'])
          : null,
      conformanceAnalytics: json['conformanceAnalytics'] != null
          ? ProcessMiningSchemaConformanceAnalytics.fromJson(
              json['conformanceAnalytics'])
          : null,
      advancedProcessIntelligence: json['advancedProcessIntelligence'] != null
          ? ProcessMiningSchemaAdvancedProcessIntelligence.fromJson(
              json['advancedProcessIntelligence'])
          : null,
      id: json['id'],
      goId: json['goId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'naturalLanguage': naturalLanguage,
      'schemaData': schemaData?.toJson(),
      'performanceDiscoveryMetrics': performanceDiscoveryMetrics?.toJson(),
      'conformanceAnalytics': conformanceAnalytics?.toJson(),
      'advancedProcessIntelligence': advancedProcessIntelligence?.toJson(),
      'id': id,
      'goId': goId,
    };
  }
}

class ProcessMiningSchemaAdvancedProcessIntelligence {
  ProcessHealthScore? processHealthScore;
  OptimizationInsights? optimizationInsights;

  ProcessMiningSchemaAdvancedProcessIntelligence({
    this.processHealthScore,
    this.optimizationInsights,
  });

  factory ProcessMiningSchemaAdvancedProcessIntelligence.fromJson(
      Map<String, dynamic> json) {
    return ProcessMiningSchemaAdvancedProcessIntelligence(
      processHealthScore: json['processHealthScore'] != null
          ? ProcessHealthScore.fromJson(json['processHealthScore'])
          : null,
      optimizationInsights: json['optimizationInsights'] != null
          ? OptimizationInsights.fromJson(json['optimizationInsights'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'processHealthScore': processHealthScore?.toJson(),
      'optimizationInsights': optimizationInsights?.toJson(),
    };
  }
}

class ProcessMiningSchemaConformanceAnalytics {
  int? complianceRate;
  ExecutionVariance? executionVariance;
  FluffyExceptionPatterns? exceptionPatterns;

  ProcessMiningSchemaConformanceAnalytics({
    this.complianceRate,
    this.executionVariance,
    this.exceptionPatterns,
  });

  factory ProcessMiningSchemaConformanceAnalytics.fromJson(
      Map<String, dynamic> json) {
    return ProcessMiningSchemaConformanceAnalytics(
      complianceRate: json['complianceRate'],
      executionVariance: json['executionVariance'] != null
          ? ExecutionVariance.fromJson(json['executionVariance'])
          : null,
      exceptionPatterns: json['exceptionPatterns'] != null
          ? FluffyExceptionPatterns.fromJson(json['exceptionPatterns'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'complianceRate': complianceRate,
      'executionVariance': executionVariance?.toJson(),
      'exceptionPatterns': exceptionPatterns?.toJson(),
    };
  }
}

class FluffyExceptionPatterns {
  CreateEmployeeRecordClass? validationFailure;

  FluffyExceptionPatterns({
    this.validationFailure,
  });

  factory FluffyExceptionPatterns.fromJson(Map<String, dynamic> json) {
    return FluffyExceptionPatterns(
      validationFailure: json['validationFailure'] != null
          ? CreateEmployeeRecordClass.fromJson(json['validationFailure'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'validationFailure': validationFailure?.toJson(),
    };
  }
}

class CreateEmployeeRecordClass {
  CreateEmployeeRecordClass();

  factory CreateEmployeeRecordClass.fromJson(Map<String, dynamic> json) {
    return CreateEmployeeRecordClass();
  }

  Map<String, dynamic> toJson() {
    return {};
  }
}

class ExecutionVariance {
  CreateEmployeeRecordClass? createEmployeeRecord;

  ExecutionVariance({
    this.createEmployeeRecord,
  });

  factory ExecutionVariance.fromJson(Map<String, dynamic> json) {
    return ExecutionVariance(
      createEmployeeRecord: json['createEmployeeRecord'] != null
          ? CreateEmployeeRecordClass.fromJson(json['createEmployeeRecord'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'createEmployeeRecord': createEmployeeRecord?.toJson(),
    };
  }
}

class ProcessMiningSchemaPerformanceDiscoveryMetrics {
  PathwayFrequency? pathwayFrequency;
  ExecutionVariance? bottleneckAnalysis;
  PurpleResourcePatterns? resourcePatterns;

  ProcessMiningSchemaPerformanceDiscoveryMetrics({
    this.pathwayFrequency,
    this.bottleneckAnalysis,
    this.resourcePatterns,
  });

  factory ProcessMiningSchemaPerformanceDiscoveryMetrics.fromJson(
      Map<String, dynamic> json) {
    return ProcessMiningSchemaPerformanceDiscoveryMetrics(
      pathwayFrequency: json['pathwayFrequency'] != null
          ? PathwayFrequency.fromJson(json['pathwayFrequency'])
          : null,
      bottleneckAnalysis: json['bottleneckAnalysis'] != null
          ? ExecutionVariance.fromJson(json['bottleneckAnalysis'])
          : null,
      resourcePatterns: json['resourcePatterns'] != null
          ? PurpleResourcePatterns.fromJson(json['resourcePatterns'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pathwayFrequency': pathwayFrequency?.toJson(),
      'bottleneckAnalysis': bottleneckAnalysis?.toJson(),
      'resourcePatterns': resourcePatterns?.toJson(),
    };
  }
}

class PathwayFrequency {
  CreateEmployeeRecordClass? newEmployeeRegistration;

  PathwayFrequency({
    this.newEmployeeRegistration,
  });

  factory PathwayFrequency.fromJson(Map<String, dynamic> json) {
    return PathwayFrequency(
      newEmployeeRegistration: json['newEmployeeRegistration'] != null
          ? CreateEmployeeRecordClass.fromJson(json['newEmployeeRegistration'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'newEmployeeRegistration': newEmployeeRegistration?.toJson(),
    };
  }
}

class PurpleResourcePatterns {
  CreateEmployeeRecordClass? hrManager;

  PurpleResourcePatterns({
    this.hrManager,
  });

  factory PurpleResourcePatterns.fromJson(Map<String, dynamic> json) {
    return PurpleResourcePatterns(
      hrManager: json['hrManager'] != null
          ? CreateEmployeeRecordClass.fromJson(json['hrManager'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hrManager': hrManager?.toJson(),
    };
  }
}

class SchemaData {
  EventLogSpecification? eventLogSpecification;

  SchemaData({
    this.eventLogSpecification,
  });

  factory SchemaData.fromJson(Map<String, dynamic> json) {
    return SchemaData(
      eventLogSpecification: json['eventLogSpecification'] != null
          ? EventLogSpecification.fromJson(json['eventLogSpecification'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'eventLogSpecification': eventLogSpecification?.toJson(),
    };
  }
}

class EventLogSpecification {
  String? caseId;
  String? activity;
  String? eventType;
  String? timestamp;
  String? resource;
  String? duration;
  Attributes? attributes;

  EventLogSpecification({
    this.caseId,
    this.activity,
    this.eventType,
    this.timestamp,
    this.resource,
    this.duration,
    this.attributes,
  });

  factory EventLogSpecification.fromJson(Map<String, dynamic> json) {
    return EventLogSpecification(
      caseId: json['caseId'],
      activity: json['activity'],
      eventType: json['eventType'],
      timestamp: json['timestamp'],
      resource: json['resource'],
      duration: json['duration'],
      attributes: json['attributes'] != null
          ? Attributes.fromJson(json['attributes'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'caseId': caseId,
      'activity': activity,
      'eventType': eventType,
      'timestamp': timestamp,
      'resource': resource,
      'duration': duration,
      'attributes': attributes?.toJson(),
    };
  }
}

class Attributes {
  String? entityState;
  String? inputValues;
  String? outputValues;
  String? executionStatus;
  String? errorDetails;

  Attributes({
    this.entityState,
    this.inputValues,
    this.outputValues,
    this.executionStatus,
    this.errorDetails,
  });

  factory Attributes.fromJson(Map<String, dynamic> json) {
    return Attributes(
      entityState: json['entityState'],
      inputValues: json['inputValues'],
      outputValues: json['outputValues'],
      executionStatus: json['executionStatus'],
      errorDetails: json['errorDetails'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'entityState': entityState,
      'inputValues': inputValues,
      'outputValues': outputValues,
      'executionStatus': executionStatus,
      'errorDetails': errorDetails,
    };
  }
}

class ProcessOwnership {
  String? naturalLanguage;
  String? originator;
  String? processOwner;
  String? businessSponsor;
  String? id;
  String? goId;

  ProcessOwnership({
    this.naturalLanguage,
    this.originator,
    this.processOwner,
    this.businessSponsor,
    this.id,
    this.goId,
  });

  factory ProcessOwnership.fromJson(Map<String, dynamic> json) {
    return ProcessOwnership(
      naturalLanguage: json['naturalLanguage'],
      originator: json['originator'],
      processOwner: json['processOwner'],
      businessSponsor: json['businessSponsor'],
      id: json['id'],
      goId: json['goId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'naturalLanguage': naturalLanguage,
      'originator': originator,
      'processOwner': processOwner,
      'businessSponsor': businessSponsor,
      'id': id,
      'goId': goId,
    };
  }
}

class RollbackPathway {
  String? id;
  String? goId;
  String? fromLo;
  String? toLo;
  String? pathwayType;
  String? naturalLanguage;

  RollbackPathway({
    this.id,
    this.goId,
    this.fromLo,
    this.toLo,
    this.pathwayType,
    this.naturalLanguage,
  });

  factory RollbackPathway.fromJson(Map<String, dynamic> json) {
    return RollbackPathway(
      id: json['id'],
      goId: json['goId'],
      fromLo: json['fromLo'],
      toLo: json['toLo'],
      pathwayType: json['pathwayType'],
      naturalLanguage: json['naturalLanguage'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'goId': goId,
      'fromLo': fromLo,
      'toLo': toLo,
      'pathwayType': pathwayType,
      'naturalLanguage': naturalLanguage,
    };
  }
}

class TriggerDefinition {
  String? naturalLanguage;
  String? triggerType;
  String? triggerCondition;
  String? triggerSchedule;
  List<String>? triggerAttributes;
  String? id;
  String? goId;

  TriggerDefinition({
    this.naturalLanguage,
    this.triggerType,
    this.triggerCondition,
    this.triggerSchedule,
    this.triggerAttributes,
    this.id,
    this.goId,
  });

  factory TriggerDefinition.fromJson(Map<String, dynamic> json) {
    return TriggerDefinition(
      naturalLanguage: json['naturalLanguage'],
      triggerType: json['triggerType'],
      triggerCondition: json['triggerCondition'],
      triggerSchedule: json['triggerSchedule'],
      triggerAttributes: json['triggerAttributes'] != null
          ? List<String>.from(json['triggerAttributes'])
          : null,
      id: json['id'],
      goId: json['goId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'naturalLanguage': naturalLanguage,
      'triggerType': triggerType,
      'triggerCondition': triggerCondition,
      'triggerSchedule': triggerSchedule,
      'triggerAttributes': triggerAttributes,
      'id': id,
      'goId': goId,
    };
  }
}
